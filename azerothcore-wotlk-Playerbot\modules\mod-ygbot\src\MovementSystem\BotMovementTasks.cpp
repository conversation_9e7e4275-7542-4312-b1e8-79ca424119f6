#include "BotMovementTasks.h"
#include "Player.h"
#include "Unit.h"
#include "MotionMaster.h"
#include "ObjectAccessor.h"
#include "Log.h"
#include <cmath>
#include <ctime>

// 静态成员初始化
uint32 BotMovementTaskFactory::nextTaskId = 1000;

// ========================================
// MoveToPositionTask 实现
// ========================================
bool MoveToPositionTask::Execute(Player* bot)
{
    if (!bot || !bot->IsAlive())
        return false;
    
    state = BOT_STATE_MOVING;
    startTime = static_cast<uint32>(time(nullptr) * 1000);

    // 验证目标位置
    auto validation = sBotTerrainValidator.ValidatePosition(bot, target.x, target.y, target.z);
    if (!validation.isValid)
    {
        LOG_WARN("movement", "MoveToPositionTask: 目标位置无效 - {}", validation.errorMessage);
        state = BOT_STATE_INTERRUPTED;
        return false;
    }

    // 生成路径
    if (!GeneratePath(bot))
    {
        LOG_WARN("movement", "MoveToPositionTask: 无法生成路径到目标位置");
        state = BOT_STATE_INTERRUPTED;
        return false;
    }
    
    // 开始移动到第一个路径点
    return MoveToNextPathPoint(bot);
}

bool MoveToPositionTask::Update(Player* bot, uint32 diff)
{
    if (!bot || state != BOT_STATE_MOVING)
        return false;

    uint32 currentTime = static_cast<uint32>(time(nullptr) * 1000);

    // 检查超时
    if (parameters.duration > 0 && currentTime - startTime > parameters.duration)
    {
        LOG_DEBUG("movement", "MoveToPositionTask: 任务超时");
        state = BOT_STATE_INTERRUPTED;
        return false;
    }

    // 检查是否到达目标
    if (IsAtDestination(bot))
    {
        LOG_DEBUG("movement", "MoveToPositionTask: 到达目标位置");
        state = BOT_STATE_COMPLETED;
        return false;
    }
    
    // 检查是否需要移动到下一个路径点
    if (currentTime - lastMoveTime > 1000) // 每秒检查一次
    {
        if (currentPathIndex < path.size())
        {
            const auto& currentTarget = path[currentPathIndex];
            float distance = CalculateDistance3D(bot->GetPositionX(), bot->GetPositionY(), bot->GetPositionZ(),
                                                currentTarget.x, currentTarget.y, currentTarget.z);
            
            if (distance < 2.0f) // 到达当前路径点
            {
                currentPathIndex++;
                if (currentPathIndex < path.size())
                {
                    MoveToNextPathPoint(bot);
                }
            }
        }
        lastMoveTime = currentTime;
    }
    
    return true;
}

void MoveToPositionTask::Stop(Player* bot)
{
    if (bot)
    {
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MoveIdle();
    }
    state = BOT_STATE_STOPPED;
}

bool MoveToPositionTask::GeneratePath(Player* bot)
{
    if (!bot)
        return false;
    
    float startX = bot->GetPositionX();
    float startY = bot->GetPositionY();
    float startZ = bot->GetPositionZ();
    
    // 使用地形验证器生成安全路径
    path = sBotTerrainValidator.GenerateSafePath(bot, startX, startY, startZ,
                                                target.x, target.y, target.z,
                                                parameters.canFly, parameters.canSwim);
    
    currentPathIndex = 0;
    return !path.empty();
}

bool MoveToPositionTask::MoveToNextPathPoint(Player* bot)
{
    if (!bot || currentPathIndex >= path.size())
        return false;
    
    const auto& nextPoint = path[currentPathIndex];
    
    // 验证下一个点是否安全
    if (parameters.enableAntiCheat)
    {
        auto validation = sBotTerrainValidator.ValidatePosition(bot, nextPoint.x, nextPoint.y, nextPoint.z);
        if (!validation.isValid)
        {
            LOG_WARN("movement", "MoveToPositionTask: 路径点不安全，跳过");
            currentPathIndex++;
            return currentPathIndex < path.size() ? MoveToNextPathPoint(bot) : false;
        }
    }
    
    // 使用MotionMaster移动
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, nextPoint.x, nextPoint.y, nextPoint.z);
    
    return true;
}

bool MoveToPositionTask::IsAtDestination(Player* bot) const
{
    if (!bot)
        return false;
    
    float distance = CalculateDistance3D(bot->GetPositionX(), bot->GetPositionY(), bot->GetPositionZ(),
                                        target.x, target.y, target.z);
    return distance < parameters.minDistance;
}

// ========================================
// FollowTargetTask 实现
// ========================================
bool FollowTargetTask::Execute(Player* bot)
{
    if (!bot || !bot->IsAlive())
        return false;
    
    Unit* target = GetTarget(bot);
    if (!target)
    {
        LOG_WARN("movement", "FollowTargetTask: 找不到跟随目标");
        return false;
    }
    
    state = BOT_STATE_MOVING;
    startTime = static_cast<uint32>(time(nullptr) * 1000);

    return MoveToFollowPosition(bot, target);
}

bool FollowTargetTask::Update(Player* bot, uint32 diff)
{
    if (!bot || state != BOT_STATE_MOVING)
        return false;

    Unit* target = GetTarget(bot);
    if (!target)
    {
        LOG_DEBUG("movement", "FollowTargetTask: 跟随目标消失");
        state = BOT_STATE_INTERRUPTED;
        return false;
    }
    
    uint32 currentTime = static_cast<uint32>(time(nullptr) * 1000);
    
    // 检查是否需要更新位置
    if (currentTime - lastUpdateTime > 2000) // 每2秒检查一次
    {
        if (ShouldUpdatePosition(bot, target))
        {
            MoveToFollowPosition(bot, target);
        }
        lastUpdateTime = currentTime;
    }
    
    return true;
}

void FollowTargetTask::Stop(Player* bot)
{
    if (bot)
    {
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MoveIdle();
    }
    state = BOT_STATE_STOPPED;
}

Unit* FollowTargetTask::GetTarget(Player* bot) const
{
    if (!bot)
        return nullptr;
    
    return ObjectAccessor::GetUnit(*bot, ObjectGuid(targetGuid));
}

bool FollowTargetTask::ShouldUpdatePosition(Player* bot, Unit* target) const
{
    if (!bot || !target)
        return false;
    
    // 检查距离
    float distance = bot->GetDistance(target);
    if (distance > followDistance + 5.0f || distance < followDistance - 2.0f)
        return true;
    
    // 检查目标是否移动了
    PathPoint currentTargetPos(target->GetPositionX(), target->GetPositionY(), target->GetPositionZ());
    if (lastTargetPosition.IsValid())
    {
        float targetMovement = lastTargetPosition.DistanceTo(currentTargetPos);
        if (targetMovement > 5.0f)
            return true;
    }
    
    return false;
}

bool FollowTargetTask::MoveToFollowPosition(Player* bot, Unit* target)
{
    if (!bot || !target)
        return false;
    
    // 计算跟随位置
    float angle = target->GetOrientation() + M_PI; // 跟在目标后面
    float x = target->GetPositionX() + cos(angle) * followDistance;
    float y = target->GetPositionY() + sin(angle) * followDistance;
    float z = target->GetPositionZ();
    
    // 修正位置到安全位置
    if (parameters.enableAntiCheat)
    {
        if (!sBotTerrainValidator.CorrectPosition(bot, x, y, z, parameters.canFly, parameters.canSwim))
        {
            LOG_WARN("movement", "FollowTargetTask: 无法找到安全的跟随位置");
            return false;
        }
    }
    
    // 移动到跟随位置
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, x, y, z);
    
    // 更新目标位置记录
    lastTargetPosition = PathPoint(target->GetPositionX(), target->GetPositionY(), target->GetPositionZ());
    
    return true;
}

// ========================================
// RandomWalkTask 实现
// ========================================
bool RandomWalkTask::Execute(Player* bot)
{
    if (!bot || !bot->IsAlive())
        return false;
    
    // 记录出生点
    homePosition = PathPoint(bot->GetPositionX(), bot->GetPositionY(), bot->GetPositionZ());
    
    state = BOT_STATE_MOVING;
    startTime = static_cast<uint32>(time(nullptr) * 1000);

    // 生成第一个随机目标
    return GenerateRandomTarget(bot) && MoveToTarget(bot);
}

bool RandomWalkTask::Update(Player* bot, uint32 diff)
{
    if (!bot || state != BOT_STATE_MOVING)
        return false;
    
    uint32 currentTime = static_cast<uint32>(time(nullptr) * 1000);
    
    // 如果在等待，检查等待时间
    if (waitTime > 0)
    {
        if (currentTime - lastMoveTime > waitTime)
        {
            waitTime = 0;
            GenerateRandomTarget(bot);
            MoveToTarget(bot);
        }
        return true;
    }
    
    // 检查是否到达目标
    if (IsAtTarget(bot))
    {
        // 随机等待一段时间
        std::uniform_int_distribution<uint32> waitDist(1000, 5000);
        waitTime = waitDist(generator);
        lastMoveTime = currentTime;
        
        LOG_DEBUG("movement", "RandomWalkTask: 到达随机目标，等待 {} 毫秒", waitTime);
    }
    
    return true;
}

void RandomWalkTask::Stop(Player* bot)
{
    if (bot)
    {
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MoveIdle();
    }
    state = BOT_STATE_STOPPED;
}

bool RandomWalkTask::GenerateRandomTarget(Player* bot)
{
    if (!bot)
        return false;
    
    // 在出生点周围生成随机位置
    std::uniform_real_distribution<float> angleDist(0.0f, 2.0f * M_PI);
    std::uniform_real_distribution<float> radiusDist(5.0f, walkRadius);
    
    float angle = angleDist(generator);
    float radius = radiusDist(generator);
    
    float x = homePosition.x + cos(angle) * radius;
    float y = homePosition.y + sin(angle) * radius;
    float z = homePosition.z;
    
    // 修正位置到安全位置
    if (parameters.enableAntiCheat)
    {
        if (!sBotTerrainValidator.CorrectPosition(bot, x, y, z, parameters.canFly, parameters.canSwim))
        {
            // 如果无法修正，尝试其他位置
            for (int attempts = 0; attempts < 5; ++attempts)
            {
                angle = angleDist(generator);
                radius = radiusDist(generator) * 0.5f; // 尝试更近的位置
                x = homePosition.x + cos(angle) * radius;
                y = homePosition.y + sin(angle) * radius;
                z = homePosition.z;
                
                if (sBotTerrainValidator.CorrectPosition(bot, x, y, z, parameters.canFly, parameters.canSwim))
                    break;
            }
        }
    }
    
    currentTarget = PathPoint(x, y, z);
    return currentTarget.IsValid();
}

bool RandomWalkTask::MoveToTarget(Player* bot)
{
    if (!bot || !currentTarget.IsValid())
        return false;
    
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, currentTarget.x, currentTarget.y, currentTarget.z);
    
    return true;
}

bool RandomWalkTask::IsAtTarget(Player* bot) const
{
    if (!bot || !currentTarget.IsValid())
        return false;
    
    float distance = CalculateDistance3D(bot->GetPositionX(), bot->GetPositionY(), bot->GetPositionZ(),
                                        currentTarget.x, currentTarget.y, currentTarget.z);
    return distance < 3.0f;
}

// ========================================
// BotMovementTaskFactory 实现
// ========================================
std::shared_ptr<MoveToPositionTask> BotMovementTaskFactory::CreateMoveToPosition(
    float x, float y, float z, const BotMovementParams& params)
{
    BotMovementTarget target(x, y, z);
    return std::make_shared<MoveToPositionTask>(GetNextTaskId(), target, params);
}

std::shared_ptr<FollowTargetTask> BotMovementTaskFactory::CreateFollowTask(
    uint64 targetGuid, float distance, const BotMovementParams& params)
{
    return std::make_shared<FollowTargetTask>(GetNextTaskId(), targetGuid, distance, params);
}

std::shared_ptr<RandomWalkTask> BotMovementTaskFactory::CreateRandomWalkTask(
    float radius, const BotMovementParams& params)
{
    return std::make_shared<RandomWalkTask>(GetNextTaskId(), radius, params);
}

std::shared_ptr<PatrolTask> BotMovementTaskFactory::CreatePatrolTask(
    const std::vector<BotWaypoint>& waypoints, const BotMovementParams& params)
{
    return std::make_shared<PatrolTask>(GetNextTaskId(), waypoints, params);
}

std::shared_ptr<FleeTask> BotMovementTaskFactory::CreateFleeTask(
    uint64 threatGuid, float distance, const BotMovementParams& params)
{
    return std::make_shared<FleeTask>(GetNextTaskId(), threatGuid, distance, params);
}

std::shared_ptr<CombatPositioningTask> BotMovementTaskFactory::CreateCombatPositioningTask(
    uint64 enemyGuid, float distance, const BotMovementParams& params)
{
    return std::make_shared<CombatPositioningTask>(GetNextTaskId(), enemyGuid, distance, params);
}

std::shared_ptr<ReturnHomeTask> BotMovementTaskFactory::CreateReturnHomeTask(
    const BotMovementParams& params)
{
    return std::make_shared<ReturnHomeTask>(GetNextTaskId(), params);
}
