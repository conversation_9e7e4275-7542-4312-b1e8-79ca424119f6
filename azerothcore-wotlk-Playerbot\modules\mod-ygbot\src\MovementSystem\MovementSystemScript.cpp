#include "MovementSystemScript.h"
#include "BotMovementManager.h"
#include "BotTerrainValidator.h"
#include "BotMovementTasks.h"
#include "Player.h"
#include "ScriptMgr.h"
#include "Log.h"
#include "Group.h"
#include "ObjectAccessor.h"
#include "Chat.h"
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// ========================================
// 移动系统脚本类
// 用于集成到AzerothCore的脚本系统
// ========================================
class BotMovementSystemScript : public PlayerScript
{
public:
    BotMovementSystemScript() : PlayerScript("BotMovementSystemScript") {}

    // 玩家登录时初始化移动系统
    void OnPlayerLogin(Player* player) override
    {
        if (!player)
            return;

        // 检查是否是机器人（这里需要根据实际的机器人检测逻辑调整）
        if (IsBot(player))
        {
            LOG_DEBUG("movement", "BotMovementSystemScript: 机器人 {} 登录，初始化移动系统", player->GetName());
            
            // 可以在这里设置默认的移动参数
            sBotMovementManager.SetDefaultSpeed(player, BOT_MOVEMENT_DEFAULT_SPEED);
        }
    }

    // 玩家登出时清理移动系统
    void OnPlayerLogout(Player* player) override
    {
        if (!player)
            return;

        if (IsBot(player))
        {
            LOG_DEBUG("movement", "BotMovementSystemScript: 机器人 {} 登出，清理移动系统", player->GetName());
            
            // 清除所有移动任务
            sBotMovementManager.ClearMovementTasks(player);
        }
    }

    // 玩家更新时更新移动系统
    void OnPlayerUpdate(Player* player, uint32 diff) override
    {
        if (!player)
            return;

        if (IsBot(player))
        {
            // 更新机器人移动
            sBotMovementManager.UpdateBot(player, diff);
        }
    }

    // 玩家死亡时停止移动
    void OnPlayerKilledByCreature(Creature* killer, Player* killed) override
    {
        if (!killed)
            return;

        if (IsBot(killed))
        {
            LOG_DEBUG("movement", "BotMovementSystemScript: 机器人 {} 死亡，停止移动", killed->GetName());
            sBotMovementManager.StopMovement(killed);
        }
    }

    // 玩家复活时可以重新开始移动
    void OnPlayerResurrect(Player* player, float restore_percent, bool applyStat) override
    {
        if (!player)
            return;

        if (IsBot(player))
        {
            LOG_DEBUG("movement", "BotMovementSystemScript: 机器人 {} 复活", player->GetName());
            
            // 可以在这里添加复活后的默认行为
            // 比如返回出生点或开始随机漫步
        }
    }

private:
    // 检查是否是机器人的辅助函数
    // 这里需要根据实际的机器人识别逻辑进行调整
    bool IsBot(Player* player) const
    {
        if (!player)
            return false;
        
        // 这里应该调用实际的机器人检测函数
        // 例如：return PlayerPatch::GetIsFaker(player);
        // 目前返回false，需要根据实际情况调整
        return false;
    }
};

// ========================================
// 世界脚本类
// 用于系统级别的初始化和更新
// ========================================
class BotMovementWorldScript : public WorldScript
{
public:
    BotMovementWorldScript() : WorldScript("BotMovementWorldScript") {}

    // 服务器启动时初始化移动系统
    void OnStartup() override
    {
        LOG_INFO("server.loading", "BotMovementWorldScript: 初始化机器人移动系统...");
        
        // 初始化移动管理器
        sBotMovementManager.Initialize();
        
        // 初始化地形验证器
        sBotTerrainValidator.Initialize();
        
        LOG_INFO("server.loading", "BotMovementWorldScript: 机器人移动系统初始化完成");
    }

    // 服务器关闭时清理移动系统
    void OnShutdown() override
    {
        LOG_INFO("server.loading", "BotMovementWorldScript: 清理机器人移动系统...");
        
        // 这里可以添加清理逻辑
        // 比如保存统计信息等
        
        LOG_INFO("server.loading", "BotMovementWorldScript: 机器人移动系统清理完成");
    }
};

// ========================================
// 简化的命令处理 - 暂时禁用复杂的命令系统
// ========================================



// ========================================
// 注册脚本
// ========================================
void AddSC_BotMovementSystem()
{
    new BotMovementSystemScript();
    new BotMovementWorldScript();
}
