#
# 机器人移动系统配置文件
# Bot Movement System Configuration
#
# 此文件包含机器人移动系统的所有配置选项
# This file contains all configuration options for the Bot Movement System
#

#
# 基本设置 (Basic Settings)
#

# 启用移动系统
# Enable the movement system
BotMovement.Enable = 1

# 启用防作弊检查
# Enable anti-cheat checks
BotMovement.AntiCheat.Enable = 1

# 启用日志记录
# Enable logging
BotMovement.Logging.Enable = 1

# 日志级别 (0=None, 1=Error, 2=Warning, 3=Info, 4=Debug, 5=Verbose)
# Log level (0=None, 1=Error, 2=Warning, 3=Info, 4=Debug, 5=Verbose)
BotMovement.Logging.Level = 2

#
# 移动参数 (Movement Parameters)
#

# 默认移动速度 (码/秒)
# Default movement speed (yards/second)
BotMovement.DefaultSpeed = 7.0

# 最大移动距离 (码)
# Maximum movement distance (yards)
BotMovement.MaxDistance = 100.0

# 最小移动距离 (码)
# Minimum movement distance (yards)
BotMovement.MinDistance = 0.5

# 更新间隔 (毫秒)
# Update interval (milliseconds)
BotMovement.UpdateInterval = 1000

# 安全检查间隔 (毫秒)
# Safety check interval (milliseconds)
BotMovement.SafetyCheckInterval = 500

#
# 地形验证设置 (Terrain Validation Settings)
#

# 地面容差 (码)
# Ground tolerance (yards)
BotMovement.Terrain.GroundTolerance = 2.0

# 水面容差 (码)
# Water tolerance (yards)
BotMovement.Terrain.WaterTolerance = 1.0

# 最大台阶高度 (码)
# Maximum step height (yards)
BotMovement.Terrain.MaxStepHeight = 2.5

# 最大坡度角度 (度)
# Maximum slope angle (degrees)
BotMovement.Terrain.MaxSlopeAngle = 50.0

# 最大攀爬角度 (度)
# Maximum climb angle (degrees)
BotMovement.Terrain.MaxClimbAngle = 60.0

#
# 飞行设置 (Flight Settings)
#

# 允许飞行移动
# Allow flight movement
BotMovement.Flight.Enable = 0

# 最小飞行高度 (码)
# Minimum flight height (yards)
BotMovement.Flight.MinHeight = 3.0

# 最大飞行高度 (码)
# Maximum flight height (yards)
BotMovement.Flight.MaxHeight = 100.0

#
# 游泳设置 (Swimming Settings)
#

# 允许游泳移动
# Allow swimming movement
BotMovement.Swimming.Enable = 1

# 最大游泳深度 (码)
# Maximum swimming depth (yards)
BotMovement.Swimming.MaxDepth = 50.0

#
# 地下移动设置 (Underground Movement Settings)
#

# 允许地下移动 (强烈建议保持禁用)
# Allow underground movement (strongly recommended to keep disabled)
BotMovement.Underground.Enable = 0

# 地下限制深度 (码)
# Underground limit depth (yards)
BotMovement.Underground.LimitDepth = 5.0

#
# 路径生成设置 (Path Generation Settings)
#

# 最大路径点数
# Maximum path points
BotMovement.Path.MaxPoints = 50

# 路径步长 (码)
# Path step size (yards)
BotMovement.Path.StepSize = 2.0

# 最大路径检查次数
# Maximum path checks
BotMovement.Path.MaxChecks = 10

# 碰撞检测点数
# Collision detection points
BotMovement.Path.CollisionChecks = 5

#
# 卡住检测设置 (Stuck Detection Settings)
#

# 启用卡住检测
# Enable stuck detection
BotMovement.StuckDetection.Enable = 1

# 卡住检测时间 (毫秒)
# Stuck detection time (milliseconds)
BotMovement.StuckDetection.Time = 5000

# 卡住判定距离 (码)
# Stuck detection distance (yards)
BotMovement.StuckDetection.Distance = 1.0

# 最大卡住次数
# Maximum stuck count
BotMovement.StuckDetection.MaxCount = 5

#
# 位置修正设置 (Position Correction Settings)
#

# 启用位置修正
# Enable position correction
BotMovement.PositionCorrection.Enable = 1

# 最大修正次数
# Maximum correction attempts
BotMovement.PositionCorrection.MaxAttempts = 3

# 修正搜索半径 (码)
# Correction search radius (yards)
BotMovement.PositionCorrection.SearchRadius = 5.0

#
# 随机漫步设置 (Random Walk Settings)
#

# 默认随机漫步半径 (码)
# Default random walk radius (yards)
BotMovement.RandomWalk.DefaultRadius = 20.0

# 最小等待时间 (毫秒)
# Minimum wait time (milliseconds)
BotMovement.RandomWalk.MinWaitTime = 1000

# 最大等待时间 (毫秒)
# Maximum wait time (milliseconds)
BotMovement.RandomWalk.MaxWaitTime = 5000

#
# 跟随设置 (Follow Settings)
#

# 默认跟随距离 (码)
# Default follow distance (yards)
BotMovement.Follow.DefaultDistance = 5.0

# 跟随更新间隔 (毫秒)
# Follow update interval (milliseconds)
BotMovement.Follow.UpdateInterval = 2000

# 跟随距离容差 (码)
# Follow distance tolerance (yards)
BotMovement.Follow.DistanceTolerance = 2.0

#
# 战斗移动设置 (Combat Movement Settings)
#

# 启用战斗移动
# Enable combat movement
BotMovement.Combat.Enable = 1

# 默认战斗距离 (码)
# Default combat distance (yards)
BotMovement.Combat.DefaultDistance = 5.0

# 战斗移动更新间隔 (毫秒)
# Combat movement update interval (milliseconds)
BotMovement.Combat.UpdateInterval = 1000

#
# 逃跑设置 (Flee Settings)
#

# 默认逃跑距离 (码)
# Default flee distance (yards)
BotMovement.Flee.DefaultDistance = 20.0

# 逃跑速度倍数
# Flee speed multiplier
BotMovement.Flee.SpeedMultiplier = 1.5

#
# 性能设置 (Performance Settings)
#

# 最大同时移动的机器人数量
# Maximum concurrent moving bots
BotMovement.Performance.MaxConcurrentBots = 100

# 性能监控启用
# Enable performance monitoring
BotMovement.Performance.Monitoring = 1

# 内存使用限制 (MB)
# Memory usage limit (MB)
BotMovement.Performance.MemoryLimit = 256

#
# 调试设置 (Debug Settings)
#

# 启用调试模式
# Enable debug mode
BotMovement.Debug.Enable = 0

# 调试级别 (1-5)
# Debug level (1-5)
BotMovement.Debug.Level = 1

# 绘制调试路径
# Draw debug paths
BotMovement.Debug.DrawPaths = 0

# 显示调试信息
# Show debug info
BotMovement.Debug.ShowInfo = 0

#
# GM命令设置 (GM Command Settings)
#

# 启用GM命令
# Enable GM commands
BotMovement.Commands.Enable = 1

# 所需权限级别
# Required permission level
BotMovement.Commands.PermissionLevel = 2

#
# 统计设置 (Statistics Settings)
#

# 启用统计收集
# Enable statistics collection
BotMovement.Statistics.Enable = 1

# 统计重置间隔 (秒)
# Statistics reset interval (seconds)
BotMovement.Statistics.ResetInterval = 3600

# 保存统计到文件
# Save statistics to file
BotMovement.Statistics.SaveToFile = 0
