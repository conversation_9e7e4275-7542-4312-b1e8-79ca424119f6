#include "BotMovementManager.h"
#include "Player.h"
#include "Unit.h"
#include "Creature.h"
#include "MotionMaster.h"
#include "Group.h"
#include "ObjectAccessor.h"
#include "Log.h"
#include "Random.h"
#include <cmath>
#include <ctime>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// ========================================
// BotMovementTask 基类实现
// ========================================
bool BotMovementTask::IsSafeToExecute(Player* bot) const
{
    if (!bot || !bot->IsAlive())
        return false;
    
    // 检查是否在战斗中且不允许战斗移动
    if (bot->IsInCombat() && movementType != BOT_MOVEMENT_COMBAT_POSITIONING &&
        movementType != BOT_MOVEMENT_FLEE)
        return false;
    
    // 检查是否被控制
    if (bot->HasUnitState(UNIT_STATE_CONTROLLED))
        return false;
    
    return true;
}

// ========================================
// BotMovementManager 实现
// ========================================
void BotMovementManager::Initialize()
{
    LOG_INFO("server", "BotMovementManager: 初始化机器人移动系统");
    
    // 初始化地形验证器
    sBotTerrainValidator.Initialize();
    
    // 清理现有状态
    botStates.clear();
    
    // 重置统计信息
    ResetStats();
    
    LOG_INFO("server", "BotMovementManager: 移动系统初始化完成");
}

void BotMovementManager::UpdateBot(Player* bot, uint32 diff)
{
    if (!bot || !bot->IsAlive())
        return;

    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    // 更新时间戳
    uint32 currentTime = static_cast<uint32>(time(nullptr) * 1000);
    state.lastUpdateTime = currentTime;
    
    // 如果暂停，不处理移动
    if (state.isPaused)
        return;
    
    // 执行安全检查
    if (antiCheatEnabled && currentTime - state.lastSafetyCheck > BOT_MOVEMENT_SAFETY_CHECK_INTERVAL)
    {
        if (!PerformSafetyCheck(bot, state))
        {
            // 安全检查失败，停止移动
            StopMovement(bot);
            stats.antiCheatBlocks++;
            return;
        }
        state.lastSafetyCheck = currentTime;
    }
    
    // 处理任务队列
    ProcessTaskQueue(bot, state);
    
    // 更新当前任务
    if (state.currentTask)
    {
        if (!state.currentTask->Update(bot, diff))
        {
            // 任务完成或失败
            LOG_DEBUG("server", "BotMovementManager: 机器人 {} 完成移动任务 {}", 
                      bot->GetName(), state.currentTask->GetTaskId());
            
            state.completedTasks++;
            stats.completedTasks++;
            state.currentTask.reset();
        }
    }
    
    // 清理已完成的任务
    CleanupCompletedTasks(bot, state);
}

uint32 BotMovementManager::AddMovementTask(Player* bot, std::shared_ptr<BotMovementTask> task)
{
    if (!bot || !task)
        return 0;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    // 检查任务是否安全
    if (!task->IsSafeToExecute(bot))
    {
        LOG_WARN("movement", "BotMovementManager: 任务不安全，拒绝执行 - 机器人: {}", bot->GetName());
        stats.failedTasks++;
        return 0;
    }
    
    // 如果是高优先级任务，清空队列
    if (task->GetPriority() >= BOT_PRIORITY_HIGH)
    {
        while (!state.taskQueue.empty())
            state.taskQueue.pop();
        
        if (state.currentTask)
        {
            state.currentTask->Stop(bot);
            state.currentTask.reset();
        }
    }
    
    state.taskQueue.push(task);
    stats.totalTasks++;
    
    LOG_DEBUG("movement", "BotMovementManager: 添加移动任务 {} 给机器人 {}", 
              task->GetTaskId(), bot->GetName());
    
    return task->GetTaskId();
}

bool BotMovementManager::RemoveMovementTask(Player* bot, uint32 taskId)
{
    if (!bot)
        return false;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto it = botStates.find(guid);
    if (it == botStates.end())
        return false;
    
    auto& state = it->second;
    
    // 检查当前任务
    if (state.currentTask && state.currentTask->GetTaskId() == taskId)
    {
        state.currentTask->Stop(bot);
        state.currentTask.reset();
        return true;
    }
    
    // 检查队列中的任务（这里简化处理，实际需要重建队列）
    // 由于std::queue不支持随机访问，这里只是标记为已处理
    return false;
}

void BotMovementManager::ClearMovementTasks(Player* bot)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto it = botStates.find(guid);
    if (it == botStates.end())
        return;
    
    auto& state = it->second;
    
    // 停止当前任务
    if (state.currentTask)
    {
        state.currentTask->Stop(bot);
        state.currentTask.reset();
    }
    
    // 清空任务队列
    while (!state.taskQueue.empty())
        state.taskQueue.pop();
    
    LOG_DEBUG("movement", "BotMovementManager: 清除机器人 {} 的所有移动任务", bot->GetName());
}

void BotMovementManager::PauseMovement(Player* bot)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    state.isPaused = true;
    
    if (state.currentTask)
        state.currentTask->Pause();
    
    LOG_DEBUG("movement", "BotMovementManager: 暂停机器人 {} 的移动", bot->GetName());
}

void BotMovementManager::ResumeMovement(Player* bot)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    state.isPaused = false;
    
    if (state.currentTask)
        state.currentTask->Resume();
    
    LOG_DEBUG("movement", "BotMovementManager: 恢复机器人 {} 的移动", bot->GetName());
}

void BotMovementManager::StopMovement(Player* bot)
{
    if (!bot)
        return;
    
    // 停止MotionMaster
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MoveIdle();
    
    // 清除移动任务
    ClearMovementTasks(bot);
    
    LOG_DEBUG("movement", "BotMovementManager: 强制停止机器人 {} 的移动", bot->GetName());
}

std::shared_ptr<BotMovementTask> BotMovementManager::GetCurrentTask(Player* bot)
{
    if (!bot)
        return nullptr;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto it = botStates.find(guid);
    if (it == botStates.end())
        return nullptr;
    
    return it->second.currentTask;
}

bool BotMovementManager::IsMoving(Player* bot)
{
    if (!bot)
        return false;
    
    // 检查MotionMaster状态
    if (bot->GetMotionMaster()->GetCurrentMovementGeneratorType() != IDLE_MOTION_TYPE)
        return true;
    
    // 检查是否有活动的移动任务
    auto task = GetCurrentTask(bot);
    return task && task->IsActive();
}

bool BotMovementManager::IsPaused(Player* bot)
{
    if (!bot)
        return false;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto it = botStates.find(guid);
    if (it == botStates.end())
        return false;
    
    return it->second.isPaused;
}

void BotMovementManager::SetDefaultSpeed(Player* bot, float speed)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    state.defaultSpeed = ClampSpeed(speed);
    
    LOG_DEBUG("movement", "BotMovementManager: 设置机器人 {} 默认速度为 {:.2f}", 
              bot->GetName(), state.defaultSpeed);
}

void BotMovementManager::SetMovementFlags(Player* bot, uint32 flags)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    state.movementFlags = flags;
    
    LOG_DEBUG("movement", "BotMovementManager: 设置机器人 {} 移动标志为 0x{:X}", 
              bot->GetName(), flags);
}

// ========================================
// 私有方法实现
// ========================================
void BotMovementManager::ProcessTaskQueue(Player* bot, BotMovementState& state)
{
    // 如果没有当前任务且队列不为空，取出下一个任务
    if (!state.currentTask && !state.taskQueue.empty())
    {
        auto nextTask = state.taskQueue.front();
        state.taskQueue.pop();
        
        if (CanExecuteTask(bot, *nextTask))
        {
            state.currentTask = nextTask;
            if (state.currentTask->Execute(bot))
            {
                LOG_DEBUG("movement", "BotMovementManager: 开始执行任务 {} - 机器人: {}", 
                          nextTask->GetTaskId(), bot->GetName());
            }
            else
            {
                LOG_WARN("movement", "BotMovementManager: 任务执行失败 {} - 机器人: {}", 
                         nextTask->GetTaskId(), bot->GetName());
                state.currentTask.reset();
                stats.failedTasks++;
            }
        }
        else
        {
            LOG_WARN("movement", "BotMovementManager: 任务无法执行 {} - 机器人: {}", 
                     nextTask->GetTaskId(), bot->GetName());
            stats.failedTasks++;
        }
    }
}

bool BotMovementManager::CanExecuteTask(Player* bot, const BotMovementTask& task)
{
    if (!bot || !bot->IsAlive())
        return false;
    
    return task.IsSafeToExecute(bot);
}

void BotMovementManager::CleanupCompletedTasks(Player* bot, BotMovementState& state)
{
    // 这里可以添加清理逻辑，比如记录统计信息等
    // 当前实现中任务完成后会自动重置currentTask
}

bool BotMovementManager::PerformSafetyCheck(Player* bot, BotMovementState& state)
{
    if (!bot || !bot->GetMap())
        return false;
    
    float x = bot->GetPositionX();
    float y = bot->GetPositionY();
    float z = bot->GetPositionZ();
    
    // 验证当前位置
    auto validation = sBotTerrainValidator.ValidatePosition(bot, x, y, z);
    if (!validation.isValid)
    {
        LOG_WARN("movement", "BotMovementManager: 机器人 {} 位置无效: {}", 
                 bot->GetName(), validation.errorMessage);
        
        // 尝试修正位置
        float correctedX = x, correctedY = y, correctedZ = z;
        if (sBotTerrainValidator.CorrectPosition(bot, correctedX, correctedY, correctedZ))
        {
            bot->TeleportTo(bot->GetMapId(), correctedX, correctedY, correctedZ, bot->GetOrientation());
            stats.positionCorrections++;
            LOG_INFO("movement", "BotMovementManager: 修正机器人 {} 位置", bot->GetName());
        }
        else
        {
            return false;
        }
    }
    
    // 检查是否卡住
    PathPoint currentPos(x, y, z);
    if (state.lastPosition.IsValid())
    {
        float distance = state.lastPosition.DistanceTo(currentPos);
        if (distance < BOT_MOVEMENT_STUCK_DISTANCE)
        {
            state.stuckCounter++;
            if (state.stuckCounter > 5) // 连续5次检查都卡住
            {
                HandleStuckBot(bot, state);
                state.stuckCounter = 0;
            }
        }
        else
        {
            state.stuckCounter = 0;
        }
    }
    
    state.lastPosition = currentPos;
    return true;
}

bool BotMovementManager::ValidateMovement(Player* bot, float x, float y, float z)
{
    return sBotTerrainValidator.ValidatePosition(bot, x, y, z).isValid;
}

void BotMovementManager::HandleStuckBot(Player* bot, BotMovementState& state)
{
    LOG_WARN("movement", "BotMovementManager: 机器人 {} 卡住，尝试解决", bot->GetName());
    
    // 停止当前移动
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MoveIdle();
    
    // 尝试找到附近的安全位置
    float x = bot->GetPositionX();
    float y = bot->GetPositionY();
    float z = bot->GetPositionZ();
    
    auto safePos = sBotTerrainValidator.GetNearestValidPosition(bot, x, y, z, 10.0f);
    if (safePos.IsValid())
    {
        bot->TeleportTo(bot->GetMapId(), safePos.x, safePos.y, safePos.z, bot->GetOrientation());
        LOG_INFO("movement", "BotMovementManager: 将卡住的机器人 {} 传送到安全位置", bot->GetName());
    }
}

// ========================================
// 便捷移动方法实现
// ========================================
uint32 BotMovementManager::MoveToPosition(Player* bot, float x, float y, float z, const BotMovementParams& params)
{
    if (!bot)
        return 0;

    // 简化实现，直接使用MotionMaster
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, x, y, z);
    return 1;
}

uint32 BotMovementManager::MoveToTarget(Player* bot, Unit* target, const BotMovementParams& params)
{
    if (!bot || !target)
        return 0;

    return MoveToPosition(bot, target->GetPositionX(), target->GetPositionY(), target->GetPositionZ(), params);
}

uint32 BotMovementManager::FollowTarget(Player* bot, Unit* target, float distance)
{
    if (!bot || !target)
        return 0;

    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MoveFollow(target, distance, 0);
    return 1;
}

uint32 BotMovementManager::FollowGroupLeader(Player* bot, float distance)
{
    if (!bot)
        return 0;

    Group* group = bot->GetGroup();
    if (!group)
        return 0;

    Player* leader = ObjectAccessor::FindPlayer(group->GetLeaderGUID());
    if (!leader)
        return 0;

    return FollowTarget(bot, leader, distance);
}

uint32 BotMovementManager::StartRandomWalk(Player* bot, float radius)
{
    if (!bot)
        return 0;

    // 简化实现：生成随机位置并移动
    float angle = frand(0, 2 * M_PI);
    float distance = frand(5.0f, radius);
    float x = bot->GetPositionX() + cos(angle) * distance;
    float y = bot->GetPositionY() + sin(angle) * distance;
    float z = bot->GetPositionZ();

    return MoveToPosition(bot, x, y, z);
}

uint32 BotMovementManager::StartPatrol(Player* bot, const std::vector<BotWaypoint>& waypoints)
{
    if (!bot || waypoints.empty())
        return 0;

    // 简化实现：移动到第一个路径点
    const auto& firstWaypoint = waypoints[0];
    return MoveToPosition(bot, firstWaypoint.x, firstWaypoint.y, firstWaypoint.z);
}

uint32 BotMovementManager::FleeFromTarget(Player* bot, Unit* target, float distance)
{
    if (!bot || !target)
        return 0;

    // 计算逃跑方向
    float angle = bot->GetAngle(target) + M_PI; // 相反方向
    float x = bot->GetPositionX() + cos(angle) * distance;
    float y = bot->GetPositionY() + sin(angle) * distance;
    float z = bot->GetPositionZ();

    return MoveToPosition(bot, x, y, z);
}
