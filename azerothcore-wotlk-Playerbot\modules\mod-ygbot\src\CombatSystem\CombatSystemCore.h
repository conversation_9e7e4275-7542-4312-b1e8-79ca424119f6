#ifndef _COMBAT_SYSTEM_CORE_H_
#define _COMBAT_SYSTEM_CORE_H_

#include "Define.h"
#include "Player.h"
#include "Unit.h"
#include "SpellMgr.h"
#include "SpellInfo.h"
#include "ObjectAccessor.h"
#include "GridNotifiers.h"
#include "Log.h"
#include <unordered_map>
#include <vector>
#include <memory>

// 战斗系统常量定义
#define COMBAT_UPDATE_INTERVAL 500          // 战斗更新间隔(毫秒)
#define COMBAT_SEARCH_RANGE 30.0f           // 搜索敌人范围
#define COMBAT_MELEE_RANGE 5.0f             // 近战攻击范围(5码以内)
#define COMBAT_RANGED_RANGE 25.0f           // 远程攻击范围
#define COMBAT_FLEE_HEALTH_PCT 20           // 逃跑血量百分比
#define COMBAT_MANA_RESERVE_PCT 10          // 法力保留百分比
#define COMBAT_MOVEMENT_TOLERANCE 0.5f      // 移动容差范围

// 战斗场景类型
enum CombatScenarioType : uint8
{
    SCENARIO_FIELD = 0,     // 野外战斗
    SCENARIO_BATTLEGROUND,  // 战场
    SCENARIO_ARENA,         // 竞技场
    SCENARIO_DUEL,          // 决斗
    SCENARIO_DUNGEON,       // 副本
    SCENARIO_RAID,          // 团队副本
    SCENARIO_MAX
};

// 战斗状态
enum CombatState : uint8
{
    COMBAT_STATE_IDLE = 0,      // 空闲
    COMBAT_STATE_ENGAGING,      // 进入战斗
    COMBAT_STATE_FIGHTING,      // 战斗中
    COMBAT_STATE_FLEEING,       // 逃跑
    COMBAT_STATE_HEALING,       // 治疗
    COMBAT_STATE_BUFFING,       // 加BUFF
    COMBAT_STATE_PURSUING,      // 追击
    COMBAT_STATE_RETREATING     // 撤退
};

// 目标优先级
enum TargetPriority : uint8
{
    PRIORITY_NONE = 0,
    PRIORITY_LOW,
    PRIORITY_NORMAL,
    PRIORITY_HIGH,
    PRIORITY_CRITICAL
};

// 法术类型
enum SpellType : uint8
{
    SPELL_TYPE_DAMAGE = 0,      // 伤害法术
    SPELL_TYPE_HEAL,            // 治疗法术
    SPELL_TYPE_BUFF,            // 增益法术
    SPELL_TYPE_DEBUFF,          // 减益法术
    SPELL_TYPE_CONTROL,         // 控制法术
    SPELL_TYPE_UTILITY,         // 工具法术
    SPELL_TYPE_AOE,             // 范围法术
    SPELL_TYPE_DOT,             // 持续伤害
    SPELL_TYPE_HOT              // 持续治疗
};

// 战斗目标信息
struct CombatTarget
{
    ObjectGuid guid;
    Unit* unit;
    float distance;
    TargetPriority priority;
    uint32 lastAttackTime;
    uint32 threatLevel;
    bool isPlayer;
    
    CombatTarget() : unit(nullptr), distance(0.0f), priority(PRIORITY_NONE), 
                    lastAttackTime(0), threatLevel(0), isPlayer(false) {}
};

// 法术信息
struct SpellInfo_Combat
{
    uint32 spellId;
    SpellType type;
    uint32 cooldown;
    uint32 manaCost;
    float range;
    uint32 castTime;
    bool requiresTarget;
    bool isAOE;
    uint32 lastUsedTime;
    uint32 priority;
    
    SpellInfo_Combat() : spellId(0), type(SPELL_TYPE_DAMAGE), cooldown(0), 
                        manaCost(0), range(0.0f), castTime(0), requiresTarget(true), 
                        isAOE(false), lastUsedTime(0), priority(0) {}
};

// 战斗统计
struct CombatStats
{
    uint32 damageDealt;
    uint32 damageTaken;
    uint32 healingDone;
    uint32 spellsCast;
    uint32 killCount;
    uint32 deathCount;
    uint32 combatTime;
    
    CombatStats() : damageDealt(0), damageTaken(0), healingDone(0), 
                   spellsCast(0), killCount(0), deathCount(0), combatTime(0) {}
};

// 前向声明
class CombatAI;

// 战斗系统核心类
class AC_GAME_API CombatSystemCore
{
public:
    static CombatSystemCore* instance();
    
    // 初始化和清理
    void Initialize();
    void Shutdown();
    
    // 战斗AI管理
    void RegisterCombatAI(Player* player, CombatScenarioType scenario);
    void UnregisterCombatAI(Player* player);
    CombatAI* GetCombatAI(Player* player);
    
    // 更新系统
    void Update(uint32 diff);
    void UpdatePlayerCombat(Player* player, uint32 diff);
    
    // 场景检测
    CombatScenarioType DetectCombatScenario(Player* player);
    
    // 工具函数
    bool IsValidCombatTarget(Player* attacker, Unit* target);
    float CalculateThreatLevel(Player* player, Unit* target);
    std::vector<Unit*> FindNearbyEnemies(Player* player, float range);

    // 玩家指令接口
    void HandlePlayerAttackCommand(Player* bot, Unit* target);
    void HandlePlayerStopAttackCommand(Player* bot);
    void HandlePlayerFollowCommand(Player* bot, Unit* target);
    void HandlePlayerDefendCommand(Player* bot, Unit* target);
    
    // 统计系统
    void RecordCombatAction(Player* player, const std::string& action, uint32 value = 0);
    CombatStats GetCombatStats(Player* player);
    void ResetCombatStats(Player* player);
    
private:
    CombatSystemCore() = default;
    ~CombatSystemCore() = default;
    
    // 单例
    static CombatSystemCore* _instance;
    
    // 战斗AI映射
    std::unordered_map<ObjectGuid, std::unique_ptr<CombatAI>> _combatAIs;
    
    // 统计数据
    std::unordered_map<ObjectGuid, CombatStats> _combatStats;
    
    // 更新计时器
    uint32 _updateTimer;
    
    // 辅助函数
    std::unique_ptr<CombatAI> CreateCombatAI(Player* player, CombatScenarioType scenario);
    void CleanupInvalidAIs();
};

// 全局访问宏
#define sCombatSystem CombatSystemCore::instance()

#endif // _COMBAT_SYSTEM_CORE_H_
