#include "BotControlCommands.h"
#include "FakePlayers.h"
#include "../Faker/Faker.h"
#include "../Faker/BotBehaviorEngine.h"
#include "../PlayerPatch.h"

// 外部函数声明
extern void AddFaker(int guid);
#include "Log.h"
#include "ObjectAccessor.h"
#include "MapMgr.h"
#include "InstanceScript.h"
#include "Channel.h"
#include "ArenaTeamMgr.h"
#include "ArenaTeam.h"
#include "Config.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include "CellImpl.h"
#include "Creature.h"
#include <algorithm>
#include <cctype>
#include "InstanceSaveMgr.h"
#include "../CombatSystem/CombatSystemCore.h"
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

void BotControlCommands::Initialize()
{
    LOG_INFO("server.loading", "初始化机器人控制命令系统...");
    LOG_INFO("server.loading", "聊天控制命令仅支持队伍聊天和私聊频道");
    InitializeChatCommands();
    InitializeWhisperCommands();

    // 启动定时检查器来测试系统
    LOG_INFO("server.loading", "启动聊天命令监听器...");

    // 旧的战斗系统已被移除
}

void BotControlCommands::InitializeChatCommands()
{
    LOG_INFO("server.loading", "初始化聊天控制命令...");

    // 基础控制命令
    _chatCommands["攻击"] = [this](Player* sender) { HandleAttackCommand(sender); };
    _chatCommands["坦克攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "tank"); };
    _chatCommands["近战攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "melee"); };
    _chatCommands["远程攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "ranged"); };
    _chatCommands["停火"] = [this](Player* sender) { HandleStopAttackCommand(sender); };
    _chatCommands["近战停火"] = [this](Player* sender) { HandleStopAttackCommand(sender, "melee"); };
    _chatCommands["远程停火"] = [this](Player* sender) { HandleStopAttackCommand(sender, "ranged"); };

    // 位置与阵型命令
    _chatCommands["集合"] = [this](Player* sender) { HandleGatherCommand(sender); };
    _chatCommands["召唤全员"] = [this](Player* sender) { HandleSummonAllCommand(sender); };
    _chatCommands["散开"] = [this](Player* sender) { HandleSpreadCommand(sender); };
    _chatCommands["驱散"] = [this](Player* sender) { HandleDispelCommand(sender); };
    _chatCommands["三角阵型"] = [this](Player* sender) { HandleTriangleFormationCommand(sender); };
    _chatCommands["圆形阵型"] = [this](Player* sender) { HandleCircleFormationCommand(sender); };
    _chatCommands["远离人群"] = [this](Player* sender) { HandleAvoidCrowdCommand(sender); };

    // 机器人管理命令
    _chatCommands["onlinefriends"] = [this](Player* sender) { HandleOnlineFriendsCommand(sender); };

    // 添加测试命令
    _chatCommands["测试"] = [this](Player* sender) {
        ChatHandler(sender->GetSession()).SendSysMessage("机器人控制系统正常工作！");
        ChatHandler(sender->GetSession()).SendSysMessage("聊天命令系统已激活，可以使用控制命令了！");
        LOG_INFO("server", "测试命令被执行 - 玩家: {}", sender->GetName());
    };

    // 添加简单的调试命令
    _chatCommands["debug"] = [this](Player* sender) {
        ChatHandler(sender->GetSession()).SendSysMessage("Debug: 命令系统工作正常");
        LOG_INFO("server", "Debug命令被执行");
    };

    // CombatAI状态命令
    _chatCommands["AI状态"] = [this](Player* sender) { HandleCombatAIStatusCommand(sender); };
    _chatCommands["技能测试"] = [this](Player* sender) { HandleSkillTestCommand(sender); };
    _chatCommands["强制施法"] = [this](Player* sender) { HandleForceSpellCommand(sender); };
    _chatCommands["战斗测试"] = [this](Player* sender) { HandleCombatTestCommand(sender); };
    _chatCommands["职业支持"] = [this](Player* sender) { HandleClassSupportCommand(sender); };

    // 添加机器人检测命令
    _chatCommands["检测机器人"] = [this](Player* sender) {
        std::vector<Player*> allNearby = GetNearbyBots(sender, 200.0f);
        ChatHandler(sender->GetSession()).PSendSysMessage("附近200码内找到 {} 个机器人", allNearby.size());

        // 列出所有附近玩家
        std::list<Player*> players;
        Acore::AnyPlayerInObjectRangeCheck checker(sender, 200.0f);
        Acore::PlayerListSearcher<Acore::AnyPlayerInObjectRangeCheck> searcher(sender, players, checker);
        Cell::VisitWorldObjects(sender, searcher, 200.0f);

        ChatHandler(sender->GetSession()).PSendSysMessage("附近总共有 {} 个玩家", players.size());

        for (Player* player : players)
        {
            bool isFaker = PlayerPatch::GetIsFaker(player);
            ChatHandler(sender->GetSession()).PSendSysMessage("玩家: {} - 机器人: {}",
                player->GetName(), isFaker ? "是" : "否");
        }
    };

    LOG_INFO("server.loading", "已注册 {} 个聊天控制命令", _chatCommands.size());
    for (const auto& cmd : _chatCommands)
    {
        LOG_DEBUG("server.loading", "注册聊天命令: '{}'", cmd.first);
    }
}

void BotControlCommands::InitializeWhisperCommands()
{
    // 私聊命令
    _whisperCommands["退队伍"] = [this](Player* bot, Player* sender) { HandleLeaveGroupCommand(bot); };

    // 竞技场战队命令 - 使用正确的索引（参考mod-jbbot的实现）
    _whisperCommands["退22战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 0); }; // 2v2 = index 0
    _whisperCommands["退33战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 1); }; // 3v3 = index 1
    _whisperCommands["退55战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 2); }; // 5v5 = index 2

    // 保留原有的按类型退出命令
    _whisperCommands["退2v2"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 2); };
    _whisperCommands["退3v3"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 3); };
    _whisperCommands["退5v5"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 5); };

    _whisperCommands["退公会"] = [this](Player* bot, Player* sender) { HandleLeaveGuildCommand(bot); };

    // 攻击命令 - 集成CombatAI系统
    _whisperCommands["攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "all"); };
    _whisperCommands["坦克攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "tank"); };
    _whisperCommands["近战攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "melee"); };
    _whisperCommands["远程攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "ranged"); };
    _whisperCommands["停止攻击"] = [this](Player* bot, Player* sender) { HandleStopAttackCommand(sender, "all"); };
    _whisperCommands["停止"] = [this](Player* bot, Player* sender) { HandleStopAttackCommand(sender, "all"); };

    // 位置控制命令
    _whisperCommands["集合"] = [this](Player* bot, Player* sender) { HandleGatherCommand(sender); };
    _whisperCommands["召唤全员"] = [this](Player* bot, Player* sender) { HandleSummonAllCommand(sender); };
    _whisperCommands["召唤"] = [this](Player* bot, Player* sender) { HandleSummonAllCommand(sender); };
    _whisperCommands["远离人群"] = [this](Player* bot, Player* sender) { HandleAvoidCrowdWhisperCommand(bot, sender); };
}

// 机器人管理命令实现
bool BotControlCommands::HandleOnlineGuildMemberCommand(Player* player)
{
    try
    {
        if (!player)
        {
            LOG_ERROR("server", "HandleOnlineGuildMemberCommand: player is null");
            return false;
        }

        Guild* guild = player->GetGuild();
        if (!guild)
        {
            ChatHandler(player->GetSession()).SendSysMessage("你必须在公会中才能使用此命令。");
            return false;
        }

        uint32 guildId = guild->GetId();
        std::string guildName = guild->GetName();

        LOG_INFO("server", "玩家 {} 请求上线公会 {} (ID: {}) 的成员", player->GetName(), guildName, guildId);

        // 正确的方法 - 查找真正的公会成员
        uint32 onlineCount = 0;
        uint32 maxCount = 5; // 限制最多上线5个

        LOG_INFO("server", "开始查找公会 {} (ID: {}) 的离线成员", guildName, guildId);

        // 直接从数据库查询公会成员，使用guild_member表
        QueryResult guildMemberResult = CharacterDatabase.Query(
            "SELECT gm.guid, c.name FROM guild_member gm "
            "JOIN characters c ON gm.guid = c.guid "
            "WHERE gm.guildid = {} AND c.online = 0 LIMIT {}",
            guildId, maxCount);

        if (!guildMemberResult)
        {
            // 如果guild_member表不存在，尝试其他方法
            LOG_INFO("server", "guild_member表查询失败，尝试alternative方法");

            // 遍历FakerMap，检查每个角色是否属于该公会
            for (auto it = FakerMap.begin(); it != FakerMap.end() && onlineCount < maxCount; ++it)
            {
                uint32 characterGuid = it->first;

                // 检查这个角色是否在线
                bool isOnline = false;
                for (auto session : FakerSessions)
                {
                    if (session && session->GetPlayer() &&
                        session->GetPlayer()->GetGUID().GetCounter() == characterGuid)
                    {
                        isOnline = true;
                        break;
                    }
                }

                if (!isOnline)
                {
                    // 检查角色是否属于该公会（通过已上线的公会成员推断）
                    QueryResult nameResult = CharacterDatabase.Query("SELECT name FROM characters WHERE guid = {}", characterGuid);
                    if (nameResult)
                    {
                        Field* fields = nameResult->Fetch();
                        std::string name = fields[0].Get<std::string>();

                        LOG_INFO("server", "尝试上线机器人: {} (GUID: {})", name, characterGuid);

                        try
                        {
                            AddFaker(characterGuid);
                            onlineCount++;
                            LOG_INFO("server", "成功上线机器人: {}", name);
                        }
                        catch (...)
                        {
                            LOG_ERROR("server", "上线机器人 {} 失败", name);
                        }
                    }
                }
            }
        }
        else
        {
            // 使用guild_member表的结果
            do
            {
                Field* fields = guildMemberResult->Fetch();
                uint32 memberGuid = fields[0].Get<uint32>();
                std::string memberName = fields[1].Get<std::string>();

                LOG_INFO("server", "找到公会离线成员: {} (GUID: {})", memberName, memberGuid);

                try
                {
                    AddFaker(memberGuid);
                    onlineCount++;
                    LOG_INFO("server", "成功上线公会成员: {}", memberName);
                }
                catch (...)
                {
                    LOG_ERROR("server", "上线公会成员 {} 失败", memberName);
                }

            } while (guildMemberResult->NextRow());
        }

        if (onlineCount == 0)
        {
            ChatHandler(player->GetSession()).SendSysMessage("没有找到可上线的公会机器人成员。");
        }
        else
        {
            ChatHandler(player->GetSession()).PSendSysMessage("已上线 {} 个机器人成员。", onlineCount);
        }

        return true;


    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "HandleOnlineGuildMemberCommand 异常: {}", e.what());
        ChatHandler(player->GetSession()).SendSysMessage("上线公会成员时发生错误。");
        return false;
    }
    catch (...)
    {
        LOG_ERROR("server", "HandleOnlineGuildMemberCommand 未知异常");
        ChatHandler(player->GetSession()).SendSysMessage("上线公会成员时发生未知错误。");
        return false;
    }
}

bool BotControlCommands::HandleOfflineAllBotCommand(Player* player)
{
    LOG_INFO("server", "玩家 {} 请求下线所有没在组队中的机器人", player->GetName());

    uint32 offlineCount = 0;
    std::vector<uint32> botsToOffline; // 收集需要下线的机器人GUID

    // 第一步：收集需要下线的机器人
    for (auto session : FakerSessions)
    {
        if (!session || !session->GetPlayer())
            continue;

        Player* bot = session->GetPlayer();

        // 检查机器人是否在队伍中
        if (!bot->GetGroup())
        {
            LOG_INFO("server", "标记下线没在组队中的机器人: {}", bot->GetName());
            botsToOffline.push_back(bot->GetGUID().GetCounter());
        }
        else
        {
            LOG_INFO("server", "跳过在队伍中的机器人: {}", bot->GetName());
        }
    }

    // 第二步：真正下线机器人 - 使用安全的迭代方式
    for (uint32 botGuid : botsToOffline)
    {
        try
        {
            // 直接通过名称下线机器人，更安全
            bool found = false;
            std::string botName;

            // 再次查找机器人确认其存在且没有队伍
            for (auto session : FakerSessions)
            {
                if (!session || !session->GetPlayer())
                    continue;

                Player* bot = session->GetPlayer();
                if (bot->GetGUID().GetCounter() == botGuid)
                {
                    // 再次确认机器人没有队伍
                    if (!bot->GetGroup())
                    {
                        botName = bot->GetName();
                        found = true;
                        break;
                    }
                    else
                    {
                        LOG_INFO("server", "机器人 {} 现在有队伍了，跳过下线", bot->GetName());
                        break;
                    }
                }
            }

            if (found && !botName.empty())
            {
                LOG_INFO("server", "正在下线机器人: {} (GUID: {})", botName, botGuid);

                // 使用Faker的Remove方法
                sFaker->Remove(botGuid);

                offlineCount++;
                LOG_INFO("server", "成功下线机器人: {}", botName);
            }
            else
            {
                LOG_DEBUG("server", "机器人 GUID {} 已不存在或有队伍", botGuid);
            }
        }
        catch (...)
        {
            LOG_ERROR("server", "下线机器人 GUID {} 失败", botGuid);
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已下线 {} 个没在组队中的机器人。", offlineCount);
    return true;
}

bool BotControlCommands::HandleOnlineFriendsCommand(Player* player)
{
    try
    {
        if (!player)
        {
            LOG_ERROR("server", "HandleOnlineFriendsCommand: player is null");
            return false;
        }

        LOG_INFO("server", "玩家 {} 执行onlinefriends命令", player->GetName());

        uint32 playerGuid = player->GetGUID().GetCounter();
        uint32 onlineCount = 0;
        uint32 maxCount = 5; // 限制最多上线5个好友

        LOG_INFO("server", "开始查找玩家 {} (GUID: {}) 的离线好友", player->GetName(), playerGuid);

        // 从数据库查询好友列表中的离线机器人
        // 使用character_social表查询好友关系
        QueryResult friendResult = CharacterDatabase.Query(
            "SELECT cs.friend, c.name FROM character_social cs "
            "JOIN characters c ON cs.friend = c.guid "
            "WHERE cs.guid = {} AND cs.flags & 1 AND c.online = 0 LIMIT {}",
            playerGuid, maxCount);

        if (!friendResult)
        {
            LOG_INFO("server", "character_social表查询失败或没有离线好友，尝试alternative方法");

            // 备用方法：从FakerMap中查找可能的好友机器人
            for (auto it = FakerMap.begin(); it != FakerMap.end() && onlineCount < maxCount; ++it)
            {
                uint32 characterGuid = it->first;

                // 检查这个角色是否在线
                bool isOnline = false;
                for (auto session : FakerSessions)
                {
                    if (session && session->GetPlayer() &&
                        session->GetPlayer()->GetGUID().GetCounter() == characterGuid)
                    {
                        isOnline = true;
                        break;
                    }
                }

                if (!isOnline)
                {
                    // 检查是否是机器人
                    QueryResult nameResult = CharacterDatabase.Query("SELECT name FROM characters WHERE guid = {}", characterGuid);
                    if (nameResult)
                    {
                        Field* fields = nameResult->Fetch();
                        std::string name = fields[0].Get<std::string>();

                        LOG_INFO("server", "尝试上线机器人好友: {} (GUID: {})", name, characterGuid);

                        try
                        {
                            AddFaker(characterGuid);
                            onlineCount++;
                            LOG_INFO("server", "成功上线机器人好友: {}", name);
                        }
                        catch (...)
                        {
                            LOG_ERROR("server", "上线机器人好友 {} 失败", name);
                        }
                    }
                }
            }
        }
        else
        {
            // 使用character_social表的结果
            do
            {
                Field* fields = friendResult->Fetch();
                uint32 friendGuid = fields[0].Get<uint32>();
                std::string friendName = fields[1].Get<std::string>();

                LOG_INFO("server", "找到离线好友: {} (GUID: {})", friendName, friendGuid);

                try
                {
                    AddFaker(friendGuid);
                    onlineCount++;
                    LOG_INFO("server", "成功上线好友: {}", friendName);
                }
                catch (...)
                {
                    LOG_ERROR("server", "上线好友 {} 失败", friendName);
                }

            } while (friendResult->NextRow());
        }

        if (onlineCount == 0)
        {
            ChatHandler(player->GetSession()).SendSysMessage("没有找到可上线的好友机器人。");
        }
        else
        {
            ChatHandler(player->GetSession()).PSendSysMessage("已上线 {} 个好友机器人。", onlineCount);
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "HandleOnlineFriendsCommand 异常: {}", e.what());
        ChatHandler(player->GetSession()).SendSysMessage("上线好友时发生错误。");
        return false;
    }
    catch (...)
    {
        LOG_ERROR("server", "HandleOnlineFriendsCommand 未知异常");
        ChatHandler(player->GetSession()).SendSysMessage("上线好友时发生未知错误。");
        return false;
    }
}

bool BotControlCommands::HandleGroupFriendCommand(Player* player)
{
    std::vector<Player*> groupBots = GetGroupBots(player);
    
    uint32 addedCount = 0;
    for (Player* bot : groupBots)
    {
        if (PlayerPatch::GetIsFaker(bot))
        {
            // 添加为好友
            if (player->GetSocial()->AddToSocialList(bot->GetGUID(), SOCIAL_FLAG_FRIEND))
            {
                addedCount++;
            }
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已将 {} 个队伍机器人添加为好友。", addedCount);
    return true;
}

bool BotControlCommands::HandleInviteFriendCommand(Player* player)
{
    if (!player->GetGroup())
    {
        ChatHandler(player->GetSession()).SendSysMessage("你必须在队伍中才能使用此命令。");
        return false;
    }

    std::vector<Player*> friendBots = GetFriendBots(player);
    Group* group = player->GetGroup();
    
    uint32 invitedCount = 0;
    for (Player* bot : friendBots)
    {
        if (PlayerPatch::GetIsFaker(bot) && !bot->GetGroup() && group->GetMembersCount() < 5)
        {
            group->AddMember(bot);
            invitedCount++;
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已邀请 {} 个好友机器人入队。", invitedCount);
    return true;
}

bool BotControlCommands::HandleAddClassBotCommand(Player* player, uint8 classId)
{
    if (classId < 1 || classId > 11 || classId == 10) // 10是无效职业
    {
        ChatHandler(player->GetSession()).SendSysMessage("无效的职业ID。有效范围：1-战士，2-圣骑，3-猎人，4-盗贼，5-牧师，6-死骑，7-萨满，8-法师，9-术士，11-德鲁伊");
        return false;
    }

    LOG_INFO("server", "玩家 {} 请求上线职业ID {} 的机器人", player->GetName(), classId);

    // 获取玩家阵营
    uint32 playerTeam = player->GetTeamId(); // 0=联盟, 1=部落
    std::string teamName = (playerTeam == 0) ? "联盟" : "部落";

    LOG_INFO("server", "玩家阵营: {} ({})", teamName, playerTeam);

    // 从数据库查找指定职业且同阵营的离线角色
    // 联盟种族: 1,3,4,7,11 (人类,矮人,暗夜精灵,侏儒,德莱尼)
    // 部落种族: 2,5,6,8,10 (兽人,亡灵,牛头人,巨魔,血精灵)
    std::string raceCondition;
    if (playerTeam == 0) // 联盟
    {
        raceCondition = "AND race IN (1,3,4,7,11)";
    }
    else // 部落
    {
        raceCondition = "AND race IN (2,5,6,8,10)";
    }

    std::string query = "SELECT guid, name, race FROM characters WHERE class = " + std::to_string(classId) +
                       " AND online = 0 " + raceCondition + " LIMIT 1";

    QueryResult result = CharacterDatabase.Query(query);

    if (!result)
    {
        ChatHandler(player->GetSession()).PSendSysMessage("没有找到职业ID为 {} 的 {} 阵营离线机器人。", classId, teamName);
        return false;
    }

    uint32 onlineCount = 0;
    std::vector<std::string> botNames;

    do
    {
        Field* fields = result->Fetch();
        uint32 guid = fields[0].Get<uint32>();
        std::string name = fields[1].Get<std::string>();
        uint8 race = fields[2].Get<uint8>();

        std::string raceName = GetRaceNameById(race);
        LOG_INFO("server", "尝试上线 {} 阵营职业ID {} 的机器人: {} ({}) (GUID: {})",
                 teamName, classId, name, raceName, guid);

        // 使用Faker系统上线机器人
        AddFaker(guid);
        botNames.push_back(name);
        onlineCount++;

    } while (result->NextRow());

    // 获取职业名称
    std::string className = GetClassNameById(classId);

    // 根据阵营设置颜色
    std::string colorCode = (playerTeam == 0) ? "|cff0080ff" : "|cffff0000"; // 联盟蓝色，部落红色
    std::string colorEnd = "|r";

    for (const std::string& botName : botNames)
    {
        // 创建带阵营颜色的可点击玩家名字链接
        ChatHandler(player->GetSession()).PSendSysMessage("{}|Hplayer:{}|h[{}]|h{} - 点击邀请入队",
            colorCode, botName.c_str(), botName.c_str(), colorEnd);
    }

    return true;
}

bool BotControlCommands::HandleResetDungeonCommand(Player* player)
{
    Group* group = player->GetGroup();
    if (!group)
    {
        ChatHandler(player->GetSession()).SendSysMessage("你必须在队伍中才能使用此命令。");
        return false;
    }

    LOG_INFO("server", "玩家 {} 请求重置队伍副本CD", player->GetName());

    uint32 resetCount = 0;
    for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
    {
        Player* member = itr->GetSource();
        if (member)
        {
            LOG_INFO("server", "重置队伍成员 {} 的副本CD", member->GetName());

            uint32 memberGuid = member->GetGUID().GetCounter();

            // 重置副本绑定 - 与instance unbind命令一致
            // 1. 清理角色副本绑定
            CharacterDatabase.Execute("DELETE FROM character_instance WHERE guid = {}", memberGuid);

            // 2. 重置每日副本次数
            CharacterDatabase.Execute("DELETE FROM character_instance_times WHERE guid = {}", memberGuid);

            // 3. 清理角色副本保存数据
            CharacterDatabase.Execute("UPDATE characters SET instance_id = 0, instance_mode_mask = 0 WHERE guid = {}", memberGuid);

            // 发送系统消息提示
            ChatHandler(member->GetSession()).SendSysMessage("你的副本CD已被重置。");

            resetCount++;
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已重置队伍中 {} 个成员的副本CD。", resetCount);
    return true;
}

// 聊天命令处理
bool BotControlCommands::HandleChatCommand(Player* sender, const std::string& message, uint32 chatType)
{
    LOG_INFO("server", "收到聊天命令: [{}] 来自玩家: {} 聊天类型: {}", message, sender->GetName(), chatType);

    auto it = _chatCommands.find(message);
    if (it != _chatCommands.end())
    {
        LOG_INFO("server", "执行聊天命令: [{}]", message);
        it->second(sender);
        ChatHandler(sender->GetSession()).SendSysMessage("执行命令: " + message);
        return true;
    }
    else
    {
        LOG_DEBUG("server", "未找到聊天命令: [{}]", message);
    }
    return false;
}

// GM命令处理（支持说话频道）
bool BotControlCommands::HandleGMCommand(Player* sender, const std::string& message)
{
    LOG_INFO("server", "收到说话频道GM命令: [{}] 来自玩家: {}", message, sender->GetName());

    // 检查是否是GM命令
    if (message == "onlineguildmember")
    {
        return HandleOnlineGuildMemberCommand(sender);
    }
    else if (message == "offlineallbot")
    {
        return HandleOfflineAllBotCommand(sender);
    }
    else if (message == "onlinefriends")
    {
        return HandleOnlineFriendsCommand(sender);
    }
    else if (message == "groupfriend")
    {
        return HandleGroupFriendCommand(sender);
    }
    else if (message == "invitefriend")
    {
        return HandleInviteFriendCommand(sender);
    }
    else if (message == "resetdungeon")
    {
        return HandleResetDungeonCommand(sender);
    }
    else if (message.find("addclassbot ") == 0)
    {
        // 解析职业ID
        std::string classIdStr = message.substr(12); // "addclassbot " 长度为12
        try
        {
            uint8 classId = std::stoi(classIdStr);
            return HandleAddClassBotCommand(sender, classId);
        }
        catch (...)
        {
            ChatHandler(sender->GetSession()).SendSysMessage("无效的职业ID，请使用1-11之间的数字");
            return false;
        }
    }

    return false;
}

// 私聊命令处理
bool BotControlCommands::HandleWhisperCommand(Player* bot, Player* sender, const std::string& message)
{
    if (!PlayerPatch::GetIsFaker(bot))
        return false;

    // 防止重复执行相同命令
    static std::unordered_map<std::string, uint32> lastCommandTime;
    std::string commandKey = std::to_string(bot->GetGUID().GetCounter()) + "_" +
                            std::to_string(sender->GetGUID().GetCounter()) + "_" + message;
    uint32 currentTime = getMSTime();

    if (lastCommandTime.count(commandKey) &&
        (currentTime - lastCommandTime[commandKey]) < 2000) // 2秒内不重复执行
    {
        LOG_DEBUG("server", "私聊命令重复执行，忽略: [{}] 对机器人: {}", message, bot->GetName());
        return true; // 返回true表示已处理，避免进一步处理
    }

    lastCommandTime[commandKey] = currentTime;

    LOG_INFO("server", "处理机器人 {} 的私聊命令: [{}] 来自玩家: {}",
        bot->GetName(), message, sender->GetName());

    auto it = _whisperCommands.find(message);
    if (it != _whisperCommands.end())
    {
        LOG_INFO("server", "执行私聊命令: [{}] 对机器人: {}", message, bot->GetName());
        it->second(bot, sender);
        return true;
    }
    else
    {
        LOG_INFO("server", "未找到私聊命令: [{}] 对机器人: {}", message, bot->GetName());
        return false;
    }
}

// 战斗控制命令实现
void BotControlCommands::HandleAttackCommand(Player* sender, const std::string& targetType)
{
    Unit* target = sender->GetSelectedUnit();
    if (!target)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("请先选择一个目标。");
        return;
    }

    std::vector<Player*> bots;

    if (targetType == "tank")
    {
        // 只让坦克职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsTankClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "melee")
    {
        // 只让近战职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsMeleeClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "ranged")
    {
        // 只让远程职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsRangedClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else
    {
        // 所有机器人攻击
        bots = GetGroupBots(sender);
    }

    uint32 attackCount = 0;
    uint32 combatAICount = 0;

    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人
        MakeBotAttackTarget(bot, target);
        attackCount++;

        // 简化统计 - 移除了CombatAI集成
        // combatAICount++; // 已移除

        LOG_INFO("server", "命令机器人 {} 攻击目标 {}", bot->GetName(), target->GetName());
    }

    // 提供详细的反馈信息
    std::string typeDesc = "";
    if (targetType == "tank") typeDesc = "坦克";
    else if (targetType == "melee") typeDesc = "近战";
    else if (targetType == "ranged") typeDesc = "远程";
    else typeDesc = "所有";

    if (attackCount == 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "没有找到符合条件的{}机器人。", typeDesc);
        return;
    }

    if (combatAICount > 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人攻击目标 {}。\n"
            "智能战斗AI: {} 个 (自动移动+职业轮换)\n"
            "传统战斗: {} 个",
            attackCount, typeDesc, target->GetName(),
            combatAICount, attackCount - combatAICount);
    }
    else
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人攻击目标 {} (传统战斗模式)。",
            attackCount, typeDesc, target->GetName());
    }
}

void BotControlCommands::HandleStopAttackCommand(Player* sender, const std::string& targetType)
{
    std::vector<Player*> bots;

    if (targetType == "melee")
    {
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsMeleeClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "ranged")
    {
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsRangedClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else
    {
        bots = GetGroupBots(sender);
    }

    uint32 stopCount = 0;
    uint32 combatAIStoppedCount = 0;

    for (Player* bot : bots)
    {
        // 简化统计 - 移除了CombatAI集成
        // combatAIStoppedCount++; // 已移除

        // GetGroupBots已经确保返回的都是机器人
        MakeBotStopAttack(bot);
        stopCount++;
        LOG_INFO("server", "命令机器人 {} 停止攻击", bot->GetName());
    }

    // 提供详细的反馈信息
    std::string typeDesc = "";
    if (targetType == "melee") typeDesc = "近战";
    else if (targetType == "ranged") typeDesc = "远程";
    else typeDesc = "所有";

    if (stopCount == 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "没有找到符合条件的{}机器人。", typeDesc);
    }
    else if (combatAIStoppedCount > 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人停止攻击。\n"
            "停止智能战斗AI: {} 个\n"
            "停止传统战斗: {} 个",
            stopCount, typeDesc, combatAIStoppedCount, stopCount - combatAIStoppedCount);
    }
    else
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人停止攻击。", stopCount, typeDesc);
    }
}

// 位置与阵型命令实现
void BotControlCommands::HandleGatherCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 gatherCount = 0;
    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人，不需要再次检查
        MakeBotFollowPlayer(bot, sender, 5.0f); // 改为5码距离，更合适
        gatherCount++;
        LOG_INFO("server", "命令机器人 {} 集合到玩家身边 (5码距离)", bot->GetName());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人集合。", gatherCount);
}

void BotControlCommands::HandleSummonAllCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    // 获取玩家当前位置信息
    float x = sender->GetPositionX();
    float y = sender->GetPositionY();
    float z = sender->GetPositionZ();
    float o = sender->GetOrientation();
    uint32 mapId = sender->GetMapId();

    LOG_INFO("server", "HandleSummonAllCommand: 召唤全员到位置 ({:.1f}, {:.1f}, {:.1f}) 地图ID: {}",
             x, y, z, mapId);

    uint32 summonCount = 0;
    uint32 crossMapCount = 0;
    uint32 failedCount = 0;

    for (Player* bot : bots)
    {
        if (!bot || !PlayerPatch::GetIsFaker(bot))
        {
            failedCount++;
            continue;
        }

        // 检查机器人当前位置
        uint32 botMapId = bot->GetMapId();

        LOG_INFO("server", "召唤机器人 {} 从地图 {} 到地图 {}",
                 bot->GetName(), botMapId, mapId);

        // 计算随机位置（在玩家周围3码范围内）
        float angle = frand(0, 2 * M_PI);
        float distance = frand(1.0f, 3.0f);
        float targetX = x + cos(angle) * distance;
        float targetY = y + sin(angle) * distance;
        float targetZ = z;

        // 强制传送机器人（支持跨地图）
        bool success = MakeBotTeleportTo(bot, targetX, targetY, targetZ, mapId);

        if (success)
        {
            summonCount++;
            if (botMapId != mapId)
            {
                crossMapCount++;
            }
        }
        else
        {
            failedCount++;
        }
    }

    // 提供详细的反馈信息
    std::string message = "召唤完成：" + std::to_string(summonCount) + " 个机器人已传送到身边";

    if (crossMapCount > 0)
    {
        message += "（其中 " + std::to_string(crossMapCount) + " 个跨地图传送）";
    }

    if (failedCount > 0)
    {
        message += "，" + std::to_string(failedCount) + " 个传送失败";
    }

    ChatHandler(sender->GetSession()).PSendSysMessage(message);

    LOG_INFO("server", "HandleSummonAllCommand: 召唤结果 - 成功: {}, 跨地图: {}, 失败: {}",
             summonCount, crossMapCount, failedCount);
}

// 技能测试命令
void BotControlCommands::HandleSkillTestCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("=== 技能系统测试 ===");

    uint32 testCount = 0;
    for (Player* bot : bots)
    {
        if (!bot || !PlayerPatch::GetIsFaker(bot))
            continue;

        uint8 classId = bot->getClass();
        if (classId != 1) // 只测试战士
        {
            ChatHandler(sender->GetSession()).PSendSysMessage(
                "机器人 {} (职业{}): 跳过测试（非战士）", bot->GetName().c_str(), classId);
            continue;
        }

        testCount++;
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "🔍 测试机器人 {} (等级{}):", bot->GetName().c_str(), bot->GetLevel());

        // 简化处理 - 移除了CombatAI集成
        ChatHandler(sender->GetSession()).PSendSysMessage("  CombatAI状态: 已移除");

        // 测试技能学习情况
        std::vector<uint32> testSpells = {78, 284, 285}; // 英勇打击的不同等级
        for (uint32 spellId : testSpells)
        {
            bool hasSpell = bot->HasSpell(spellId);
            ChatHandler(sender->GetSession()).PSendSysMessage(
                "  技能 {}: {}", spellId, hasSpell ? "已学会" : "未学会");
        }

        // 检查资源
        uint32 rage = bot->GetPower(POWER_RAGE);
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "  当前怒气: {}", rage);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage(
        "🧪 测试完成，共测试了 {} 个战士机器人", testCount);
}

// 强制施法测试命令
void BotControlCommands::HandleForceSpellCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    Unit* target = sender->GetSelectedUnit();
    if (!target)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("请先选择一个目标。");
        return;
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("=== 强制施法测试 ===");

    for (Player* bot : bots)
    {
        if (!bot || !PlayerPatch::GetIsFaker(bot))
            continue;

        uint8 classId = bot->getClass();
        if (classId != 1) // 只测试战士
            continue;

        ChatHandler(sender->GetSession()).PSendSysMessage(
            "测试机器人 {} 强制施法:", bot->GetName().c_str());

        // 测试英勇打击 (78)
        uint32 testSpellId = 78;

        // 检查是否学会
        bool hasSpell = bot->HasSpell(testSpellId);
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "  技能 {}: {}", testSpellId, hasSpell ? "已学会" : "未学会");

        if (!hasSpell)
            continue;

        // 简化处理 - 移除了CombatAI集成

        // 直接调用CastSpell测试
        SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(testSpellId);
        if (spellInfo)
        {
            // 使用简化的施法方式
            Spell* spell = new Spell(bot, spellInfo, TRIGGERED_NONE, ObjectGuid::Empty);
            spell->m_CastItem = nullptr;

            SpellCastTargets targets;
            targets.SetUnitTarget(target);

            bot->SetFacingToObject(target);
            bot->SetSelection(target->GetGUID());

            SpellCastResult result = spell->prepare(&targets, nullptr);

            ChatHandler(sender->GetSession()).PSendSysMessage(
                "  施法结果: {} ({})",
                result == SPELL_CAST_OK ? "成功" : "失败",
                static_cast<int>(result));
        }
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("强制施法测试完成");
}

// 战斗测试命令
void BotControlCommands::HandleCombatTestCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    Unit* target = sender->GetSelectedUnit();
    if (!target)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("请先选择一个目标。");
        return;
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("=== 战斗测试 ===");

    for (Player* bot : bots)
    {
        if (!bot || !PlayerPatch::GetIsFaker(bot))
            continue;

        uint8 classId = bot->getClass();
        if (classId != 1) // 只测试战士
            continue;

        ChatHandler(sender->GetSession()).PSendSysMessage(
            "测试机器人 {} 战斗状态:", bot->GetName().c_str());

        // 简化处理 - 移除了CombatAI集成
        // 设置目标并开始攻击
        bot->SetTarget(target->GetGUID());
        bot->Attack(target, true);

        // 检查状态
        bool inCombat = bot->IsInCombat();
        bool hasVictim = bot->GetVictim() != nullptr;
        bool hasTarget = bot->GetSelectedUnit() != nullptr;

        ChatHandler(sender->GetSession()).PSendSysMessage(
            "  战斗状态: {}, 攻击目标: {}, 选择目标: {}",
            inCombat ? "是" : "否",
            hasVictim ? "是" : "否",
            hasTarget ? "是" : "否");
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("战斗测试完成");
}

void BotControlCommands::HandleSpreadCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 spreadCount = 0;
    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人
        float angle = frand(0, 2 * M_PI);
        float distance = frand(5.0f, 15.0f);
        float x = sender->GetPositionX() + cos(angle) * distance;
        float y = sender->GetPositionY() + sin(angle) * distance;
        float z = sender->GetPositionZ();

        MakeBotMoveTo(bot, x, y, z);
        spreadCount++;
        LOG_INFO("server", "命令机器人 {} 散开到位置 ({:.1f}, {:.1f})", bot->GetName(), x, y);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人散开。", spreadCount);
}

void BotControlCommands::HandleDispelCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 dispelCount = 0;
    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人
        // 这里可以添加驱散逻辑，比如使用特定法术
        // 暂时只是发送消息
        dispelCount++;
        LOG_INFO("server", "命令机器人 {} 进行驱散", bot->GetName());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人进行驱散。", dispelCount);
}

void BotControlCommands::HandleTriangleFormationCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    if (bots.size() < 2)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("附近机器人数量不足，无法组成三角阵型。");
        return;
    }

    float baseX = sender->GetPositionX();
    float baseY = sender->GetPositionY();
    float baseZ = sender->GetPositionZ();

    uint32 formationCount = 0;
    for (size_t i = 0; i < bots.size() && i < 6; ++i)
    {
        // GetGroupBots已经确保返回的都是机器人
        float angle = (i * 60.0f) * M_PI / 180.0f; // 每60度一个位置
        float distance = 8.0f;
        float x = baseX + cos(angle) * distance;
        float y = baseY + sin(angle) * distance;

        MakeBotMoveTo(bots[i], x, y, baseZ);
        formationCount++;
        LOG_INFO("server", "命令机器人 {} 移动到三角阵型位置 ({:.1f}, {:.1f})", bots[i]->GetName(), x, y);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人组成三角阵型。", formationCount);
}

void BotControlCommands::HandleCircleFormationCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    if (bots.size() < 3)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("附近机器人数量不足，无法组成圆形阵型。");
        return;
    }

    float baseX = sender->GetPositionX();
    float baseY = sender->GetPositionY();
    float baseZ = sender->GetPositionZ();
    float radius = 10.0f;

    uint32 formationCount = 0;
    for (size_t i = 0; i < bots.size(); ++i)
    {
        // GetGroupBots已经确保返回的都是机器人
        float angle = (i * 2.0f * M_PI) / bots.size();
        float x = baseX + cos(angle) * radius;
        float y = baseY + sin(angle) * radius;

        MakeBotMoveTo(bots[i], x, y, baseZ);
        formationCount++;
        LOG_INFO("server", "命令机器人 {} 移动到圆形阵型位置 ({:.1f}, {:.1f})", bots[i]->GetName(), x, y);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人组成圆形阵型。", formationCount);
}

void BotControlCommands::HandleAvoidCrowdCommand(Player* sender)
{
    LOG_INFO("server", "执行远离人群命令，玩家: {}", sender->GetName());
    std::vector<Player*> bots = GetGroupBots(sender);

    LOG_INFO("server", "获取到机器人数量: {}", bots.size());

    uint32 avoidCount = 0;
    for (Player* bot : bots)
    {
        LOG_INFO("server", "处理机器人: {}", bot->GetName());

        // bots列表中的都应该是机器人，直接执行命令
        // 让机器人远离人群，移动到较远的位置
        float angle = frand(0, 2 * M_PI);
        float distance = frand(20.0f, 40.0f);
        float x = sender->GetPositionX() + cos(angle) * distance;
        float y = sender->GetPositionY() + sin(angle) * distance;
        float z = sender->GetPositionZ();

        LOG_INFO("server", "命令机器人 {} 移动到位置 ({}, {}, {})", bot->GetName(), x, y, z);
        MakeBotMoveTo(bot, x, y, z);
        avoidCount++;
    }

    LOG_INFO("server", "远离人群命令完成，影响机器人数量: {}", avoidCount);
    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人远离人群。", avoidCount);
}

// 私聊命令处理实现
void BotControlCommands::HandleLeaveGroupCommand(Player* bot)
{
    if (!bot->GetGroup())
        return;

    Group* group = bot->GetGroup();
    group->RemoveMember(bot->GetGUID());

    LOG_INFO("server", "机器人 {} 收到私聊命令，已退出队伍", bot->GetName());
}

void BotControlCommands::HandleAvoidCrowdWhisperCommand(Player* bot, Player* sender)
{
    if (!bot || !sender)
        return;

    // 只有接收到私聊的机器人才执行命令
    LOG_INFO("server", "机器人 {} 收到来自 {} 的私聊'远离人群'命令", bot->GetName(), sender->GetName());

    // 计算远离位置
    float avoidDistance = 15.0f; // 远离距离
    float angle = frand(0, 2 * M_PI); // 随机角度

    float x = bot->GetPositionX() + avoidDistance * std::cos(angle);
    float y = bot->GetPositionY() + avoidDistance * std::sin(angle);
    float z = bot->GetPositionZ();

    // 移动到远离位置
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, x, y, z);

    LOG_INFO("server", "机器人 {} 执行远离人群命令，移动到位置 ({:.1f}, {:.1f}, {:.1f})",
             bot->GetName(), x, y, z);
}

void BotControlCommands::HandleLeaveArenaTeamCommand(Player* bot, uint8 teamType)
{
    LOG_INFO("server", "机器人 {} 尝试退出 {}v{} 竞技场战队", bot->GetName(), teamType, teamType);

    uint32 arenaTeamId = 0;

    // 调试：显示机器人当前的所有竞技场战队信息
    uint32 team2v2 = bot->GetArenaTeamId(ARENA_TEAM_2v2);
    uint32 team3v3 = bot->GetArenaTeamId(ARENA_TEAM_3v3);
    uint32 team5v5 = bot->GetArenaTeamId(ARENA_TEAM_5v5);
    LOG_INFO("server", "机器人 {} 当前竞技场战队: 2v2={}, 3v3={}, 5v5={}",
        bot->GetName(), team2v2, team3v3, team5v5);

    switch (teamType)
    {
        case 2:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_2v2);
            LOG_INFO("server", "尝试退出2v2战队，ID: {}", arenaTeamId);
            break;
        case 3:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_3v3);
            LOG_INFO("server", "尝试退出3v3战队，ID: {}", arenaTeamId);
            break;
        case 5:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_5v5);
            LOG_INFO("server", "尝试退出5v5战队，ID: {}", arenaTeamId);
            break;
        default:
            LOG_ERROR("server", "无效的竞技场战队类型: {}", teamType);
            return;
    }

    if (arenaTeamId == 0)
    {
        LOG_INFO("server", "机器人 {} 没有加入 {}v{} 竞技场战队", bot->GetName(), teamType, teamType);
        return;
    }

    ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
    if (arenaTeam)
    {
        LOG_INFO("server", "找到竞技场战队: {} (ID: {})", arenaTeam->GetName(), arenaTeamId);
        arenaTeam->DelMember(bot->GetGUID(), true);
        LOG_INFO("server", "机器人 {} 成功退出 {}v{} 竞技场战队: {}",
            bot->GetName(), teamType, teamType, arenaTeam->GetName());
    }
    else
    {
        LOG_ERROR("server", "找不到竞技场战队 ID: {}", arenaTeamId);
    }
}

void BotControlCommands::HandleLeaveArenaTeamByName(Player* bot, const std::string& teamName)
{
    LOG_INFO("server", "机器人 {} 尝试按名称退出竞技场战队: [{}]", bot->GetName(), teamName);

    // 检查所有竞技场战队类型
    std::vector<std::pair<uint8, ArenaTeamTypes>> teamTypes = {
        {2, ARENA_TEAM_2v2},
        {3, ARENA_TEAM_3v3},
        {5, ARENA_TEAM_5v5}
    };

    bool foundTeam = false;
    for (const auto& teamTypePair : teamTypes)
    {
        uint32 arenaTeamId = bot->GetArenaTeamId(teamTypePair.second);
        if (arenaTeamId != 0)
        {
            ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
            if (arenaTeam)
            {
                std::string currentTeamName = arenaTeam->GetName();
                LOG_INFO("server", "检查战队: {} (ID: {}) vs 目标: {}",
                    currentTeamName, arenaTeamId, teamName);

                // 检查战队名称是否匹配（支持部分匹配）
                if (currentTeamName.find(teamName) != std::string::npos)
                {
                    arenaTeam->DelMember(bot->GetGUID(), true);
                    LOG_INFO("server", "机器人 {} 成功退出竞技场战队: {} ({}v{})",
                        bot->GetName(), currentTeamName, teamTypePair.first, teamTypePair.first);
                    foundTeam = true;
                    break;
                }
            }
        }
    }

    if (!foundTeam)
    {
        LOG_INFO("server", "机器人 {} 没有找到名称包含 [{}] 的竞技场战队", bot->GetName(), teamName);

        // 显示机器人当前所有战队信息
        LOG_INFO("server", "机器人 {} 当前竞技场战队信息:", bot->GetName());
        for (const auto& teamTypePair : teamTypes)
        {
            uint32 arenaTeamId = bot->GetArenaTeamId(teamTypePair.second);
            if (arenaTeamId != 0)
            {
                ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
                if (arenaTeam)
                {
                    LOG_INFO("server", "  {}v{}: {} (ID: {})",
                        teamTypePair.first, teamTypePair.first, arenaTeam->GetName(), arenaTeamId);
                }
            }
        }
    }
}

void BotControlCommands::HandleLeaveArenaTeamByIndex(Player* bot, uint8 teamIndex)
{
    // 使用正确的方法（参考mod-jbbot的实现）
    LOG_INFO("server", "机器人 {} 尝试退出竞技场战队，索引: {}", bot->GetName(), teamIndex);

    // 直接使用GetArenaTeamId(index)方法
    uint32 arenaTeamId = bot->GetArenaTeamId(teamIndex);

    if (arenaTeamId == 0)
    {
        std::string teamTypeName;
        switch (teamIndex)
        {
            case 0: teamTypeName = "2v2"; break;
            case 1: teamTypeName = "3v3"; break;
            case 2: teamTypeName = "5v5"; break;
            default: teamTypeName = "未知"; break;
        }
        LOG_INFO("server", "机器人 {} 没有加入 {} 竞技场战队", bot->GetName(), teamTypeName);
        return;
    }

    ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
    if (arenaTeam)
    {
        std::string teamName = arenaTeam->GetName();
        std::string teamTypeName;
        switch (teamIndex)
        {
            case 0: teamTypeName = "2v2"; break;
            case 1: teamTypeName = "3v3"; break;
            case 2: teamTypeName = "5v5"; break;
            default: teamTypeName = "未知"; break;
        }

        // 退出竞技场战队
        arenaTeam->DelMember(bot->GetGUID(), true);
        LOG_INFO("server", "机器人 {} 成功退出 {} 竞技场战队: {} (ID: {})",
            bot->GetName(), teamTypeName, teamName, arenaTeamId);
    }
    else
    {
        LOG_ERROR("server", "找不到竞技场战队 ID: {}", arenaTeamId);
    }
}

void BotControlCommands::HandleLeaveGuildCommand(Player* bot)
{
    LOG_INFO("server", "机器人 {} 尝试退出公会", bot->GetName());

    if (!bot->GetGuild())
    {
        LOG_INFO("server", "机器人 {} 没有加入公会", bot->GetName());
        return;
    }

    Guild* guild = bot->GetGuild();
    std::string guildName = guild->GetName();
    guild->DeleteMember(bot->GetGUID(), false, false);

    LOG_INFO("server", "机器人 {} 成功退出公会: {}", bot->GetName(), guildName);
}

// 辅助函数实现
std::vector<Player*> BotControlCommands::GetNearbyBots(Player* player, float range)
{
    std::vector<Player*> bots;

    LOG_INFO("server", "开始搜索附近机器人，范围: {} 码", range);

    std::list<Player*> players;
    Acore::AnyPlayerInObjectRangeCheck checker(player, range);
    Acore::PlayerListSearcher<Acore::AnyPlayerInObjectRangeCheck> searcher(player, players, checker);
    Cell::VisitWorldObjects(player, searcher, range);

    LOG_INFO("server", "找到附近玩家数量: {}", players.size());

    for (Player* nearbyPlayer : players)
    {
        // 排除发起命令的玩家自己
        if (nearbyPlayer == player)
        {
            LOG_INFO("server", "跳过命令发起者: {}", nearbyPlayer->GetName());
            continue;
        }

        LOG_INFO("server", "检查玩家: {} 是否为机器人", nearbyPlayer->GetName());

        bool isFaker = PlayerPatch::GetIsFaker(nearbyPlayer);
        bool isInFakerMap = FakerMap.find(nearbyPlayer->GetGUID().GetCounter()) != FakerMap.end();
        bool isInFakerSessions = false;

        // 检查是否在FakerSessions中
        for (auto session : FakerSessions)
        {
            if (session && session->GetPlayer() &&
                session->GetPlayer()->GetGUID() == nearbyPlayer->GetGUID())
            {
                isInFakerSessions = true;
                break;
            }
        }

        // 检查名字是否包含机器人特征
        std::string playerName = nearbyPlayer->GetName();
        bool hasRobotName = (playerName.find("bot") != std::string::npos ||
                            playerName.find("机器人") != std::string::npos ||
                            playerName.find("rndbot") != std::string::npos );

        LOG_INFO("server", "玩家 {} 检测结果: IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={}",
            nearbyPlayer->GetName(), isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

        // 额外检查：如果玩家在FakerMap中但不在FakerSessions中，可能是真实玩家被误标记
        if (isInFakerMap && !isInFakerSessions && !hasRobotName)
        {
            LOG_INFO("server", "玩家 {} 可能被误标记为机器人，跳过", nearbyPlayer->GetName());
            continue;
        }

        // 多重检测：需要至少两种方式确认是机器人，或者有明显的机器人名字特征
        if ((isFaker && (isInFakerMap || isInFakerSessions)) ||
            (isInFakerMap && isInFakerSessions) ||
            hasRobotName)
        {
            bots.push_back(nearbyPlayer);
            LOG_INFO("server", "确认机器人: {} (通过多重检测)", nearbyPlayer->GetName());

            // 如果通过其他方式检测到是机器人，但PlayerPatch没有标记，则补充标记
            if (!isFaker)
            {
                PlayerPatch::SetIsFaker(nearbyPlayer, true);
                LOG_INFO("server", "补充标记玩家 {} 为机器人", nearbyPlayer->GetName());
            }
        }
        else
        {
            LOG_INFO("server", "非机器人: {}", nearbyPlayer->GetName());
        }
    }

    LOG_INFO("server", "最终找到机器人数量: {}", bots.size());
    return bots;
}

std::vector<Player*> BotControlCommands::GetGroupBots(Player* player)
{
    std::vector<Player*> bots;

    Group* group = player->GetGroup();
    if (!group)
    {
        LOG_INFO("server", "玩家 {} 不在队伍中", player->GetName());
        return bots;
    }

    LOG_INFO("server", "开始搜索队伍中的机器人，队伍成员数: {}", group->GetMembersCount());

    for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
    {
        Player* member = itr->GetSource();
        if (!member || member == player)
            continue;

        LOG_INFO("server", "检查队伍成员: {} 是否为机器人", member->GetName());

        bool isFaker = PlayerPatch::GetIsFaker(member);
        bool isInFakerMap = FakerMap.find(member->GetGUID().GetCounter()) != FakerMap.end();
        bool isInFakerSessions = false;

        // 检查是否在FakerSessions中
        for (auto session : FakerSessions)
        {
            if (session && session->GetPlayer() &&
                session->GetPlayer()->GetGUID() == member->GetGUID())
            {
                isInFakerSessions = true;
                break;
            }
        }

        // 检查名字是否包含机器人特征
        std::string playerName = member->GetName();
        bool hasRobotName = (playerName.find("bot") != std::string::npos ||
                            playerName.find("机器人") != std::string::npos ||
                            playerName.find("rndbot") != std::string::npos );

        LOG_INFO("server", "队伍成员 {} 检测结果: IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={}",
            member->GetName(), isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

        // 强力检测：检查是否为真实玩家
        bool isRealPlayer = false;

        // 检查是否有真实的客户端连接（非机器人特征）
        if (member->GetSession())
        {
            // 检查IP地址是否为本地（机器人通常使用本地连接）
            std::string sessionIP = member->GetSession()->GetRemoteAddress();
            if (sessionIP != "127.0.0.1" && sessionIP != "localhost" && sessionIP != "::1")
            {
                isRealPlayer = true;
            }
        }

        // 如果不是真实玩家，或者有任何机器人特征，就认为是机器人
        if (!isRealPlayer || isFaker || isInFakerMap || isInFakerSessions || hasRobotName)
        {
            bots.push_back(member);
            LOG_INFO("server", "确认队伍机器人: {} (检测方式: IsRealPlayer={}, IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={})",
                member->GetName(), isRealPlayer, isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

            // 确保机器人被正确标记
            if (!isFaker)
            {
                PlayerPatch::SetIsFaker(member, true);
                LOG_INFO("server", "补充标记队伍成员 {} 为机器人", member->GetName());
            }

            // 确保机器人在FakerMap中
            if (!isInFakerMap)
            {
                FakerMap[member->GetGUID().GetCounter()] = true;
                LOG_INFO("server", "将队伍成员 {} 添加到FakerMap", member->GetName());
            }
        }
        else
        {
            LOG_INFO("server", "队伍成员 {} 是真实玩家", member->GetName());
        }
    }

    LOG_INFO("server", "最终找到队伍机器人数量: {}", bots.size());
    return bots;
}

std::string BotControlCommands::GetClassNameById(uint8 classId)
{
    switch (classId)
    {
        case 1: return "战士";
        case 2: return "圣骑士";
        case 3: return "猎人";
        case 4: return "盗贼";
        case 5: return "牧师";
        case 6: return "死亡骑士";
        case 7: return "萨满";
        case 8: return "法师";
        case 9: return "术士";
        case 11: return "德鲁伊";
        default: return "未知职业";
    }
}

std::string BotControlCommands::GetRaceNameById(uint8 raceId)
{
    switch (raceId)
    {
        // 联盟种族
        case 1: return "人类";
        case 3: return "矮人";
        case 4: return "暗夜精灵";
        case 7: return "侏儒";
        case 11: return "德莱尼";

        // 部落种族
        case 2: return "兽人";
        case 5: return "亡灵";
        case 6: return "牛头人";
        case 8: return "巨魔";
        case 10: return "血精灵";

        default: return "未知种族";
    }
}

bool BotControlCommands::IsPluginMessage(const std::string& message)
{
    // 检查是否为空消息
    if (message.empty())
        return true;

    // 常见插件消息特征
    static const std::vector<std::string> pluginKeywords = {
        "ezCollections",
        "GTFO_v",
        "LibGroupTalents",
        "DBMv4-Ver",
        "BWVQ3",
        "RAL_SELECT",
        "RAL_SELECTREPLY",
        "Absorbs_",
        "SpecializedAbsorbs_",
        "questie",
        "CAIO",
        "AIO",
        "^1^S",
        "msgVer",
        "msgId",
        "!ver",
        "VERSION:",
        "HELLO",
        "Hi!",
        "startcheck"
    };

    // 检查消息是否包含插件关键词
    for (const std::string& keyword : pluginKeywords)
    {
        if (message.find(keyword) != std::string::npos)
        {
            return true;
        }
    }

    // 检查是否包含特殊字符组合（插件通信常用）
    if (message.find("\t") != std::string::npos ||  // Tab字符
        message.find("{{") != std::string::npos ||   // 双大括号
        message.find("}}") != std::string::npos ||   // 双大括号
        message.find("^T^") != std::string::npos ||  // 特殊分隔符
        message.find("^N") != std::string::npos ||   // 特殊分隔符
        message.find("^S") != std::string::npos)     // 特殊分隔符
    {
        return true;
    }

    // 检查是否为纯数字或特殊格式
    if (message.length() > 10 &&
        (message.find_first_not_of("0123456789") == std::string::npos ||
         message.find("%") != std::string::npos))
    {
        return true;
    }

    return false;
}

std::vector<Player*> BotControlCommands::GetGuildBots(Player* player)
{
    std::vector<Player*> bots;

    Guild* guild = player->GetGuild();
    if (!guild)
        return bots;

    // 简化实现：从附近的机器人中筛选公会成员
    std::vector<Player*> nearbyBots = GetNearbyBots(player, 200.0f);

    for (Player* bot : nearbyBots)
    {
        if (bot->GetGuildId() == guild->GetId())
        {
            bots.push_back(bot);
        }
    }

    return bots;
}

std::vector<Player*> BotControlCommands::GetFriendBots(Player* player)
{
    std::vector<Player*> bots;

    // 简化实现：从附近的机器人中筛选，而不是从好友列表
    // 这样避免了访问私有成员的问题
    std::vector<Player*> nearbyBots = GetNearbyBots(player, 200.0f);

    PlayerSocial* social = player->GetSocial();
    if (!social)
        return nearbyBots; // 如果没有社交系统，返回附近所有机器人

    for (Player* bot : nearbyBots)
    {
        if (social->HasFriend(bot->GetGUID()))
        {
            bots.push_back(bot);
        }
    }

    return bots;
}

std::vector<Player*> BotControlCommands::GetBotsByClass(uint8 classId, uint32 maxCount)
{
    std::vector<Player*> bots;

    // 从FakePlayersVec中查找指定职业的机器人
    uint32 count = 0;
    for (const auto& fakePlayer : FakePlayersVec)
    {
        if (count >= maxCount)
            break;

        if (fakePlayer.class_ == classId)
        {
            Player* bot = ObjectAccessor::FindPlayerByName(fakePlayer.pname);
            if (bot && PlayerPatch::GetIsFaker(bot))
            {
                bots.push_back(bot);
                count++;
            }
        }
    }

    return bots;
}

bool BotControlCommands::IsMeleeClass(uint8 classId)
{
    return classId == CLASS_WARRIOR || classId == CLASS_PALADIN ||
           classId == CLASS_ROGUE || classId == CLASS_DEATH_KNIGHT;
}

bool BotControlCommands::IsRangedClass(uint8 classId)
{
    return classId == CLASS_HUNTER || classId == CLASS_PRIEST ||
           classId == CLASS_SHAMAN || classId == CLASS_MAGE ||
           classId == CLASS_WARLOCK || classId == CLASS_DRUID;
}

bool BotControlCommands::IsTankClass(uint8 classId)
{
    return classId == CLASS_WARRIOR || classId == CLASS_PALADIN || classId == CLASS_DEATH_KNIGHT;
}

void BotControlCommands::SendOpcodeToBot(Player* bot, uint16 opcode, WorldPacket& packet)
{
    if (!bot || !bot->GetSession() || !PlayerPatch::GetIsFaker(bot))
        return;

    // 简化实现：直接发送数据包到客户端
    bot->GetSession()->SendPacket(&packet);
}

void BotControlCommands::MakeBotAttackTarget(Player* bot, Unit* target)
{
    if (!bot || !target || !PlayerPatch::GetIsFaker(bot))
        return;

    LOG_INFO("server", "MakeBotAttackTarget: 机器人 {} 攻击目标 {}", bot->GetName(), target->GetName());

    // 使用新的高级战斗系统处理攻击指令
    sCombatSystem->HandlePlayerAttackCommand(bot, target);

    // 基础攻击设置（确保兼容性）
    bot->SetTarget(target->GetGUID());
    bot->Attack(target, true);
    bot->SetFacingToObject(target);

    // 新的战斗系统会自动处理移动和攻击范围，这里不需要额外的移动逻辑
    LOG_DEBUG("combat", "MakeBotAttackTarget: 机器人 {} 已启动高级战斗AI攻击 {}",
             bot->GetName(), target->GetName());
    return; // 直接返回，让新战斗系统处理后续逻辑

    /*
    // 以下旧的移动逻辑已被新的高级战斗系统替代
    // 新系统会自动处理攻击范围、移动跟随、面向目标等所有逻辑
    */

    // 5. 设置战斗模式
    if (sBotBehaviorEngine)
    {
        sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_COMBAT);
    }

    // 6. 旧的战斗更新定时器已被新的高级战斗系统替代
    // StartCombatUpdateTimer(bot, target);

    LOG_INFO("server", "MakeBotAttackTarget: 机器人 {} 攻击设置完成，开始战斗", bot->GetName());
}

// StartCombatUpdateTimer函数已被删除 - 旧战斗系统已被新的高级战斗系统替代

void BotControlCommands::UpdateCombatMovement(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    // 检查目标是否还有效
    if (!target->IsAlive() || !bot->IsValidAttackTarget(target))
    {
        LOG_INFO("server", "UpdateCombatMovement: 目标 {} 已无效，停止战斗", target->GetName());
        MakeBotStopAttack(bot);
        return;
    }

    float distance = bot->GetDistance(target);
    uint8 classId = bot->getClass();
    bool isMeleeClass = IsMeleeClass(classId);

    if (isMeleeClass)
    {
        // 近战职业需要持续跟随
        // 使用与MakeBotAttackTarget相同的攻击距离计算
        float attackRange = 4.5f;
        if (target->GetCombatReach() > 1.5f)
        {
            attackRange = 5.5f;
        }

        if (distance > attackRange + 1.5f)
        {
            // 距离过远，重新追击到3码距离
            float chaseDistance = 3.0f;
            LOG_DEBUG("server", "UpdateCombatMovement: 近战机器人 {} 重新追击目标 {} (当前距离: {:.1f}, 追击到: {:.1f})",
                     bot->GetName(), target->GetName(), distance, chaseDistance);

            bot->GetMotionMaster()->Clear();
            bot->GetMotionMaster()->MoveChase(target, chaseDistance);
        }

        // 确保面向目标
        if (!bot->HasInArc(M_PI / 6, target)) // 30度角度容差
        {
            bot->SetFacingToObject(target);
        }
    }
    else
    {
        // 远程职业保持距离
        float optimalRange = 20.0f;

        if (distance > optimalRange + 10.0f)
        {
            // 距离太远，靠近一些
            LOG_DEBUG("server", "UpdateCombatMovement: 远程机器人 {} 靠近目标 {} (距离: {:.1f})",
                     bot->GetName(), target->GetName(), distance);

            bot->GetMotionMaster()->Clear();
            bot->GetMotionMaster()->MoveChase(target, optimalRange);
        }
        else if (distance < 8.0f)
        {
            // 距离太近，后退
            LOG_DEBUG("server", "UpdateCombatMovement: 远程机器人 {} 后退避开目标 {} (距离: {:.1f})",
                     bot->GetName(), target->GetName(), distance);

            float angle = target->GetAngle(bot) + M_PI;
            float x = bot->GetPositionX() + cos(angle) * 12.0f;
            float y = bot->GetPositionY() + sin(angle) * 12.0f;
            float z = bot->GetPositionZ();

            bot->GetMotionMaster()->Clear();
            bot->GetMotionMaster()->MovePoint(0, x, y, z);
        }
    }
}

void BotControlCommands::MakeBotStopAttack(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用新的高级战斗系统处理停止攻击指令
    sCombatSystem->HandlePlayerStopAttackCommand(bot);

    // 旧的战斗系统清理代码已被移除

    // 停止所有战斗相关行为
    bot->AttackStop();
    bot->InterruptNonMeleeSpells(false);
    bot->SetTarget(ObjectGuid::Empty);

    // 停止战斗移动
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MoveIdle();

    // 重置行为模式
    if (sBotBehaviorEngine)
    {
        sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
    }

    LOG_INFO("server", "MakeBotStopAttack: 机器人 {} 停止攻击完成", bot->GetName());
}

// UpdateActiveCombats函数已被删除 - 旧战斗系统已被新的高级战斗系统替代

void BotControlCommands::MakeBotMoveTo(Player* bot, float x, float y, float z)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用正常移动方式
    bot->GetMotionMaster()->MovePoint(0, x, y, z);

    LOG_DEBUG("server", "命令机器人 {} 移动到位置 ({}, {}, {})", bot->GetName(), x, y, z);
}

bool BotControlCommands::MakeBotTeleportTo(Player* bot, float x, float y, float z, uint32 mapId)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return false;

    LOG_INFO("server", "MakeBotTeleportTo: 开始传送机器人 {} 到位置 ({:.1f}, {:.1f}, {:.1f}) 地图: {}",
             bot->GetName(), x, y, z, mapId);

    try
    {
        // 停止机器人当前的所有行为
        bot->GetMotionMaster()->Clear();
        bot->InterruptNonMeleeSpells(false);
        bot->AttackStop();

        // 如果机器人在战斗中，强制脱离战斗
        if (bot->IsInCombat())
        {
            bot->CombatStop();
            LOG_INFO("server", "MakeBotTeleportTo: 机器人 {} 强制脱离战斗", bot->GetName());
        }

        // 执行传送
        bool success = bot->TeleportTo(mapId, x, y, z, bot->GetOrientation());

        if (success)
        {
            LOG_INFO("server", "MakeBotTeleportTo: 机器人 {} 传送成功", bot->GetName());

            // 传送后的清理工作
            bot->SetTarget(ObjectGuid::Empty);

            return true;
        }
        else
        {
            LOG_WARN("server", "MakeBotTeleportTo: 机器人 {} 传送失败 - TeleportTo返回false", bot->GetName());
            return false;
        }
    }
    catch (...)
    {
        LOG_ERROR("server", "MakeBotTeleportTo: 机器人 {} 传送时发生异常", bot->GetName());
        return false;
    }
}

// 重载方法：简化版本（同地图传送）
void BotControlCommands::MakeBotTeleportTo(Player* bot, float x, float y, float z)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用增强版本，传送到当前地图
    MakeBotTeleportTo(bot, x, y, z, bot->GetMapId());
}

void BotControlCommands::MakeBotFollowPlayer(Player* bot, Player* target, float distance)
{
    if (!bot || !target || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用MotionMaster的跟随功能，而不是单次移动
    bot->GetMotionMaster()->Clear();

    // 尝试使用FollowMovementGenerator
    if (target->IsInWorld())
    {
        // 使用核心的跟随系统
        bot->GetMotionMaster()->MoveFollow(target, distance, frand(0, 2 * M_PI));
        LOG_DEBUG("server", "机器人 {} 开始跟随玩家 {} (距离: {:.1f})",
                  bot->GetName(), target->GetName(), distance);
    }
    else
    {
        // 回退到位置移动
        float angle = frand(0, 2 * M_PI);
        float x = target->GetPositionX() + cos(angle) * distance;
        float y = target->GetPositionY() + sin(angle) * distance;
        float z = target->GetPositionZ();

        MakeBotMoveTo(bot, x, y, z);
        LOG_DEBUG("server", "机器人 {} 移动到玩家 {} 附近 (回退模式)", bot->GetName(), target->GetName());
    }
}

void BotControlCommands::SetBotGroupFollowMode(Player* bot, bool enable)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    if (enable)
    {
        // 停止所有移动和行为，开启队伍跟随模式
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MoveIdle();

        // 停止攻击
        bot->AttackStop();
        bot->InterruptNonMeleeSpells(false);

        // 清除目标
        bot->SetTarget(ObjectGuid::Empty);

        // 强制设置机器人为IDLE模式，确保BotBehaviorEngine不会生成随机行为
        if (sBotBehaviorEngine)
        {
            sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
        }

        // 在机器人身上设置一个标记，表示正在跟随模式
        bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED); // 使用和平标记

        /*LOG_INFO("server", "机器人 {} 开启队伍跟随模式，停止所有行为，设置IDLE模式", bot->GetName());*/
    }
    else
    {
        // 恢复随机移动
        bot->GetMotionMaster()->Clear();

        // 移除和平标记
        bot->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);

        // 恢复探索模式
        if (sBotBehaviorEngine)
        {
            sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_EXPLORING);
        }

        bot->GetMotionMaster()->MoveRandom(10.0f);
        /*LOG_INFO("server", "机器人 {} 恢复随机移动模式，设置EXPLORING模式", bot->GetName());*/
    }
}

void BotControlCommands::UpdateBotGroupFollow()
{
    // 遍历所有在线机器人，检查其队伍状态
    for (auto session : FakerSessions)
    {
        if (!session || !session->GetPlayer())
            continue;

        Player* bot = session->GetPlayer();
        Group* group = bot->GetGroup();

        if (group)
        {
            // 检查机器人是否在战斗中，如果在战斗中则不干扰
            bool inCombat = bot->IsInCombat();
            bool hasTarget = bot->GetTarget() && bot->GetSelectedUnit();
            bool usingCombatAI = false; // 已移除CombatAI集成
            bool isAttacking = bot->GetVictim() != nullptr;

            // 特殊检查：是否正在攻击训练假人
            bool attackingTrainingDummy = false;
            if (hasTarget)
            {
                Unit* target = bot->GetSelectedUnit();
                if (target && target->ToCreature())
                {
                    Creature* creature = target->ToCreature();
                    std::string name = creature->GetName();
                    std::transform(name.begin(), name.end(), name.begin(), ::tolower);
                    if (name.find("training") != std::string::npos ||
                        name.find("target") != std::string::npos ||
                        name.find("dummy") != std::string::npos)
                    {
                        attackingTrainingDummy = true;
                    }
                }
            }

            // 如果机器人在战斗中或有攻击目标或正在攻击或攻击训练假人，跳过跟随逻辑
            if (inCombat || hasTarget || usingCombatAI || isAttacking || attackingTrainingDummy)
            {
                LOG_DEBUG("server", "机器人 {} 在战斗中，跳过跟随逻辑 (战斗:{}, 目标:{}, AI:{}, 攻击:{}, 训练假人:{})",
                         bot->GetName().c_str(), inCombat, hasTarget, usingCombatAI, isAttacking, attackingTrainingDummy);
                continue;
            }

            // 机器人在队伍中且不在战斗，执行跟随逻辑
            SetBotGroupFollowMode(bot, true);

            // 设置机器人为空闲模式（仅在非战斗时）
            if (sBotBehaviorEngine)
            {
                sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
            }

            // 找到队长并跟随
            Player* leader = ObjectAccessor::FindPlayer(group->GetLeaderGUID());
            if (leader && leader != bot && !PlayerPatch::GetIsFaker(leader))
            {
                // 跟随真实玩家队长
                float distance = bot->GetDistance(leader);
                if (distance > 8.0f) // 距离超过8码时跟随
                {
                    // 开始跟随 - 保持在5码距离
                    MakeBotFollowPlayer(bot, leader, 5.0f);

                    LOG_DEBUG("server", "机器人 {} 跟随队长 {} (距离: {:.1f})",
                        bot->GetName(), leader->GetName(), distance);
                }
                else if (distance < 3.0f)
                {
                    // 距离太近时停止移动
                    bot->GetMotionMaster()->Clear();
                    bot->GetMotionMaster()->MoveIdle();
                }
                else if (distance >= 3.0f && distance <= 8.0f)
                {
                    // 在理想距离范围内，保持待机
                    if (bot->GetMotionMaster()->GetCurrentMovementGeneratorType() != IDLE_MOTION_TYPE)
                    {
                        bot->GetMotionMaster()->Clear();
                        bot->GetMotionMaster()->MoveIdle();
                    }
                }

                // 每次更新都强制检查和停止随机行为
                LOG_DEBUG("server", "机器人 {} 在队伍中，强制保持跟随状态", bot->GetName());
            }
        }
        else
        {
            // 机器人不在队伍中，恢复随机移动
            SetBotGroupFollowMode(bot, false);

            // 恢复探索模式
            if (sBotBehaviorEngine)
            {
                sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_EXPLORING);
            }
        }
    }
}

// 聊天脚本实现
void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg)
{
    LOG_INFO("server", "OnPlayerChat(基础): 玩家 {} 说: '{}' 类型: {}", player->GetName(), msg, type);

    // 处理说话频道中的GM命令
    if (type == CHAT_MSG_SAY)
    {
        LOG_INFO("server", "处理说话频道GM命令");
        sBotControlCommands->HandleGMCommand(player, msg);
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Player* receiver)
{
    // 过滤插件通信消息
    if (sBotControlCommands->IsPluginMessage(msg))
    {
        return; // 忽略插件通信
    }

    std::string receiverName = receiver ? receiver->GetName() : "无";
    LOG_INFO("server", "OnPlayerChat(私聊): 玩家 {} 对 {} 说: [{}] 类型: {}",
        player->GetName(), receiverName, msg, type);

    // 处理私聊 - 支持机器人控制命令和机器人私聊命令
    if (type == CHAT_MSG_WHISPER && receiver)
    {
        // 如果接收者是机器人，处理机器人私聊命令
        if (PlayerPatch::GetIsFaker(receiver))
        {
            LOG_INFO("server", "处理机器人私聊命令");
            sBotControlCommands->HandleWhisperCommand(receiver, player, msg);
        }
        // 如果发送者想通过私聊控制机器人，也支持控制命令
        else
        {
            LOG_INFO("server", "处理私聊控制命令");
            sBotControlCommands->HandleChatCommand(player, msg, type);
        }
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Group* group)
{
    // 增强的插件消息过滤
    if (sBotControlCommands->IsPluginMessage(msg))
    {
        return; // 忽略插件通信
    }

    LOG_INFO("server", "OnPlayerChat(队伍): 玩家 {} 在队伍中说: '{}' 类型: {} 队伍: {}",
        player->GetName(), msg, type, group ? "有" : "无");

    // 处理队伍聊天和团队聊天 - 添加类型51支持
    if (type == CHAT_MSG_PARTY || type == CHAT_MSG_RAID || type == 51)
    {
        LOG_INFO("server", "处理队伍聊天控制命令: '{}'", msg);
        bool handled = sBotControlCommands->HandleChatCommand(player, msg, type);
        LOG_INFO("server", "命令处理结果: {}", handled ? "成功" : "未找到命令");
    }
    else
    {
        LOG_INFO("server", "忽略非队伍聊天消息，类型: {}", type);
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Guild* guild)
{
    // 不处理公会聊天
    return;
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Channel* channel)
{
    // 不处理频道聊天
    return;
}

// GM命令脚本实现
Acore::ChatCommands::ChatCommandTable BotControlCommandScript::GetCommands() const
{
    using namespace Acore::ChatCommands;

    static ChatCommandTable botControlCommandTable =
    {
        { "onlineguildmember", HandleOnlineGuildMemberCommand, SEC_PLAYER, Console::No },
        { "offlineallbot", HandleOfflineAllBotCommand, SEC_PLAYER, Console::No },
        { "onlinefriends", HandleOnlineFriendsCommand, SEC_PLAYER, Console::No },
        { "groupfriend", HandleGroupFriendCommand, SEC_PLAYER, Console::No },
        { "invitefriend", HandleInviteFriendCommand, SEC_PLAYER, Console::No },
        { "addclassbot", HandleAddClassBotCommand, SEC_PLAYER, Console::No },
        { "resetdungeon", HandleResetDungeonCommand, SEC_PLAYER, Console::No },
        { "test", HandleTestCommand, SEC_PLAYER, Console::No },
    };

    static ChatCommandTable commandTable =
    {
        { "bot", botControlCommandTable },
    };

    return commandTable;
}

bool BotControlCommandScript::HandleOnlineGuildMemberCommand(ChatHandler* handler, const char* args)
{
    LOG_INFO("server", "执行GM命令: onlineguildmember");
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOnlineGuildMemberCommand(player);
}

bool BotControlCommandScript::HandleOfflineAllBotCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOfflineAllBotCommand(player);
}

bool BotControlCommandScript::HandleOnlineFriendsCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOnlineFriendsCommand(player);
}

bool BotControlCommandScript::HandleGroupFriendCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleGroupFriendCommand(player);
}

bool BotControlCommandScript::HandleInviteFriendCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleInviteFriendCommand(player);
}

bool BotControlCommandScript::HandleAddClassBotCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    if (!args || !*args)
    {
        handler->SendSysMessage("用法: .bot addclass [职业ID]");
        handler->SendSysMessage("职业ID: 1-战士，2-圣骑，3-猎人，4-盗贼，5-牧师，6-死骑，7-萨满，8-法师，9-术士，11-德鲁伊");
        return false;
    }

    uint8 classId = atoi(args);
    return sBotControlCommands->HandleAddClassBotCommand(player, classId);
}

bool BotControlCommandScript::HandleResetDungeonCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleResetDungeonCommand(player);
}

bool BotControlCommandScript::HandleTestCommand(ChatHandler* handler, const char* args)
{
    LOG_INFO("server", "执行GM测试命令: test");
    handler->SendSysMessage("机器人控制GM命令系统正常工作！");
    return true;
}

// 世界脚本用于初始化系统
class BotControlWorldScript : public WorldScript
{
public:
    BotControlWorldScript() : WorldScript("BotControlWorldScript") {}

    void OnStartup() override
    {
        sBotControlCommands->Initialize();
        LOG_INFO("server.loading", "机器人控制命令系统已初始化");
        LOG_INFO("server.loading", "请在队伍聊天中输入 '测试' 来验证系统是否工作");
    }

    void OnUpdate(uint32 diff) override
    {
        static uint32 updateTimer = 0;
        static uint32 combatUpdateTimer = 0;

        updateTimer += diff;
        combatUpdateTimer += diff;

        // 每1秒更新一次机器人跟随状态（更频繁的检查）
        if (updateTimer >= 1000)
        {
            sBotControlCommands->UpdateBotGroupFollow();
            updateTimer = 0;
        }

        // 旧的战斗系统更新已被删除 - 现在使用新的高级战斗系统
    }
};





// CombatAI状态检查命令
void BotControlCommands::HandleCombatAIStatusCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("=== CombatAI系统状态检查 ===");

    uint32 totalBots = 0;
    uint32 supportedBots = 0;
    uint32 enabledBots = 0;

    for (Player* bot : bots)
    {
        totalBots++;

        uint8 classId = bot->getClass();
        std::string className = "";
        switch (classId)
        {
            case 1: className = "战士"; break;
            case 2: className = "圣骑士"; break;
            case 3: className = "猎人"; break;
            case 4: className = "盗贼"; break;
            case 5: className = "牧师"; break;
            case 7: className = "萨满"; break;
            case 8: className = "法师"; break;
            case 9: className = "术士"; break;
            case 11: className = "德鲁伊"; break;
            default: className = "未知"; break;
        }

        // 简化处理 - 移除了CombatAI集成
        bool shouldEnable = false;
        bool isEnabled = false;
        bool shouldUse = false;

        // if (shouldEnable) supportedBots++;
        // if (isEnabled) enabledBots++;

        std::string status = "";
        if (shouldEnable && isEnabled && shouldUse)
        {
            status = "完全支持";
        }
        else if (shouldEnable && isEnabled)
        {
            status = "已启用但未激活";
        }
        else if (shouldEnable)
        {
            status = "支持但未启用";
        }
        else
        {
            status = "不支持";
        }

        ChatHandler(sender->GetSession()).PSendSysMessage(
            "{} ({}) - {}",
            bot->GetName().c_str(), className.c_str(), status.c_str());

        LOG_INFO("server", "CombatAI状态检查: {} (职业{}) - ShouldEnable:{}, IsEnabled:{}, ShouldUse:{}",
                 bot->GetName(), classId, shouldEnable, isEnabled, shouldUse);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage(
        "总计: {} 个机器人, {} 个支持CombatAI, {} 个已启用",
        totalBots, supportedBots, enabledBots);

    ChatHandler(sender->GetSession()).PSendSysMessage(
        "支持的职业: 所有职业 (1-11, 除职业10)");
}

// ✅ 职业支持检查命令
void BotControlCommands::HandleClassSupportCommand(Player* sender)
{
    if (!sender)
        return;

    ChatHandler(sender->GetSession()).PSendSysMessage("=== CombatAI职业支持检查 ===");

    // 检查所有职业的支持情况
    std::vector<std::pair<uint8, std::string>> allClasses = {
        {1, "战士"}, {2, "圣骑士"}, {3, "猎人"}, {4, "盗贼"}, {5, "牧师"},
        {6, "死亡骑士"}, {7, "萨满"}, {8, "法师"}, {9, "术士"}, {11, "德鲁伊"}
    };

    for (const auto& classInfo : allClasses)
    {
        uint8 classId = classInfo.first;
        std::string className = classInfo.second;

        // 检查是否支持
        bool supports = false;
        switch (classId)
        {
            case 1: case 2: case 3: case 4: case 5:
            case 6: case 7: case 8: case 9: case 11:
                supports = true;
                break;
            default:
                supports = false;
                break;
        }

        std::string status = supports ? "✅ 支持" : "❌ 不支持";

        ChatHandler(sender->GetSession()).PSendSysMessage(
            "职业{} ({}) - {}",
            classId, className.c_str(), status.c_str());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("=== 职业支持检查完成 ===");
}

// 脚本注册函数
void AddSC_BotControlCommands()
{
    new BotControlWorldScript();
    new BotControlChatScript();
    new BotControlCommandScript();
}
