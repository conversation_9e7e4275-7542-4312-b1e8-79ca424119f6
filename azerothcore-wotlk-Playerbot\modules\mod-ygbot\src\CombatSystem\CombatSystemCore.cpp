#include "CombatSystemCore.h"
#include "CombatAI.h"
#include "BotSkillManager.h"
#include "Player.h"
#include "Map.h"
#include "Battleground.h"
#include "Log.h"

// 单例实例
CombatSystemCore* CombatSystemCore::_instance = nullptr;

CombatSystemCore* CombatSystemCore::instance()
{
    if (!_instance)
        _instance = new CombatSystemCore();
    return _instance;
}

void CombatSystemCore::Initialize()
{
    LOG_INFO("server.loading", "初始化战斗系统核心...");

    _updateTimer = 0;
    _combatAIs.clear();
    _combatStats.clear();

    // 初始化技能管理器
    sBotSkillManager->Initialize();

    LOG_INFO("server.loading", "战斗系统核心初始化完成");
}

void CombatSystemCore::Shutdown()
{
    LOG_INFO("server.loading", "关闭战斗系统核心...");

    // 关闭技能管理器
    sBotSkillManager->Shutdown();

    _combatAIs.clear();
    _combatStats.clear();

    if (_instance)
    {
        delete _instance;
        _instance = nullptr;
    }

    LOG_INFO("server.loading", "战斗系统核心已关闭");
}

void CombatSystemCore::RegisterCombatAI(Player* player, CombatScenarioType scenario)
{
    if (!player)
        return;
    
    ObjectGuid playerGuid = player->GetGUID();
    
    // 如果已存在，先移除
    auto it = _combatAIs.find(playerGuid);
    if (it != _combatAIs.end())
    {
        _combatAIs.erase(it);
    }
    
    // 创建新的战斗AI
    auto combatAI = CreateCombatAI(player, scenario);
    if (combatAI)
    {
        _combatAIs[playerGuid] = std::move(combatAI);
        
        // 初始化统计数据
        _combatStats[playerGuid] = CombatStats();
        
        LOG_DEBUG("combat", "为玩家 {} 注册了战斗AI (场景: {})", 
                 player->GetName(), static_cast<uint32>(scenario));
    }
}

void CombatSystemCore::UnregisterCombatAI(Player* player)
{
    if (!player)
        return;
    
    ObjectGuid playerGuid = player->GetGUID();
    
    auto it = _combatAIs.find(playerGuid);
    if (it != _combatAIs.end())
    {
        _combatAIs.erase(it);
        LOG_DEBUG("combat", "移除了玩家 {} 的战斗AI", player->GetName());
    }
    
    auto statsIt = _combatStats.find(playerGuid);
    if (statsIt != _combatStats.end())
    {
        _combatStats.erase(statsIt);
    }
}

CombatAI* CombatSystemCore::GetCombatAI(Player* player)
{
    if (!player)
        return nullptr;
    
    auto it = _combatAIs.find(player->GetGUID());
    if (it != _combatAIs.end())
        return it->second.get();
    
    return nullptr;
}

void CombatSystemCore::Update(uint32 diff)
{
    _updateTimer += diff;
    
    // 每500ms更新一次
    if (_updateTimer >= COMBAT_UPDATE_INTERVAL)
    {
        // 清理无效的AI
        CleanupInvalidAIs();
        
        // 更新所有战斗AI
        for (auto& pair : _combatAIs)
        {
            if (pair.second)
            {
                Player* player = ObjectAccessor::FindPlayer(pair.first);
                if (player && player->IsInWorld())
                {
                    UpdatePlayerCombat(player, _updateTimer);
                }
            }
        }
        
        _updateTimer = 0;
    }
}

void CombatSystemCore::UpdatePlayerCombat(Player* player, uint32 diff)
{
    if (!player)
        return;
    
    CombatAI* combatAI = GetCombatAI(player);
    if (!combatAI)
    {
        // 自动检测场景并注册AI
        CombatScenarioType scenario = DetectCombatScenario(player);
        if (scenario != SCENARIO_FIELD) // 只有非野外场景才自动注册
        {
            RegisterCombatAI(player, scenario);
            combatAI = GetCombatAI(player);
        }
    }
    
    if (combatAI)
    {
        combatAI->Update(diff);
    }
}

CombatScenarioType CombatSystemCore::DetectCombatScenario(Player* player)
{
    if (!player)
        return SCENARIO_FIELD;
    
    // 检查是否在决斗
    if (player->duel)
        return SCENARIO_DUEL;
    
    // 检查是否在竞技场
    if (player->InArena())
        return SCENARIO_ARENA;
    
    // 检查是否在战场
    if (player->InBattleground())
        return SCENARIO_BATTLEGROUND;
    
    // 检查是否在副本
    Map* map = player->GetMap();
    if (map)
    {
        if (map->IsDungeon())
            return SCENARIO_DUNGEON;
        if (map->IsRaid())
            return SCENARIO_RAID;
    }
    
    // 默认为野外
    return SCENARIO_FIELD;
}

bool CombatSystemCore::IsValidCombatTarget(Player* attacker, Unit* target)
{
    if (!attacker || !target)
        return false;
    
    // 基本检查
    if (!target->IsAlive())
        return false;
    
    if (!attacker->IsValidAttackTarget(target))
        return false;
    
    // 距离检查
    if (attacker->GetDistance(target) > COMBAT_SEARCH_RANGE)
        return false;
    
    // 场景特定检查
    CombatScenarioType scenario = DetectCombatScenario(attacker);
    switch (scenario)
    {
        case SCENARIO_DUEL:
            // 决斗中只能攻击决斗对手
            return attacker->duel && attacker->duel->Opponent == target;
            
        case SCENARIO_ARENA:
        case SCENARIO_BATTLEGROUND:
            // 竞技场和战场中检查阵营
            return attacker->IsHostileTo(target);
            
        default:
            return true;
    }
}

float CombatSystemCore::CalculateThreatLevel(Player* player, Unit* target)
{
    if (!player || !target)
        return 0.0f;
    
    float threat = 0.0f;
    
    // 基础威胁值
    if (target->IsPlayer())
    {
        Player* targetPlayer = target->ToPlayer();
        
        // 根据职业计算威胁
        switch (targetPlayer->getClass())
        {
            case CLASS_PRIEST:
            case CLASS_PALADIN:
            case CLASS_SHAMAN:
            case CLASS_DRUID:
                threat += 100.0f; // 治疗职业高威胁
                break;
            case CLASS_MAGE:
            case CLASS_WARLOCK:
                threat += 80.0f; // 法系DPS中等威胁
                break;
            case CLASS_ROGUE:
            case CLASS_HUNTER:
                threat += 70.0f; // 物理DPS中等威胁
                break;
            case CLASS_WARRIOR:
            case CLASS_DEATH_KNIGHT:
                threat += 50.0f; // 坦克职业较低威胁
                break;
        }
        
        // 根据血量调整威胁
        uint32 healthPct = targetPlayer->GetHealthPct();
        if (healthPct < 30)
            threat += 50.0f; // 残血目标威胁增加
        
        // 根据距离调整威胁
        float distance = player->GetDistance(target);
        if (distance < 10.0f)
            threat += 30.0f; // 近距离威胁增加
    }
    else
    {
        // NPC威胁计算
        threat = static_cast<float>(target->GetLevel() * 10);
    }
    
    return threat;
}

std::vector<Unit*> CombatSystemCore::FindNearbyEnemies(Player* player, float range)
{
    std::vector<Unit*> enemies;
    
    if (!player)
        return enemies;
    
    // 搜索附近的敌对单位
    std::list<Unit*> targets;
    Acore::AnyUnfriendlyUnitInObjectRangeCheck check(player, player, range);
    Acore::UnitListSearcher<Acore::AnyUnfriendlyUnitInObjectRangeCheck> searcher(player, targets, check);
    Cell::VisitAllObjects(player, searcher, range);
    
    for (Unit* target : targets)
    {
        if (IsValidCombatTarget(player, target))
        {
            enemies.push_back(target);
        }
    }
    
    return enemies;
}

void CombatSystemCore::RecordCombatAction(Player* player, const std::string& action, uint32 value)
{
    if (!player)
        return;
    
    ObjectGuid playerGuid = player->GetGUID();
    auto it = _combatStats.find(playerGuid);
    if (it == _combatStats.end())
    {
        _combatStats[playerGuid] = CombatStats();
        it = _combatStats.find(playerGuid);
    }
    
    CombatStats& stats = it->second;
    
    if (action == "damage_dealt")
        stats.damageDealt += value;
    else if (action == "damage_taken")
        stats.damageTaken += value;
    else if (action == "healing_done")
        stats.healingDone += value;
    else if (action == "spell_cast")
        stats.spellsCast++;
    else if (action == "kill")
        stats.killCount++;
    else if (action == "death")
        stats.deathCount++;
}

CombatStats CombatSystemCore::GetCombatStats(Player* player)
{
    if (!player)
        return CombatStats();
    
    auto it = _combatStats.find(player->GetGUID());
    if (it != _combatStats.end())
        return it->second;
    
    return CombatStats();
}

void CombatSystemCore::ResetCombatStats(Player* player)
{
    if (!player)
        return;
    
    auto it = _combatStats.find(player->GetGUID());
    if (it != _combatStats.end())
    {
        it->second = CombatStats();
    }
}

std::unique_ptr<CombatAI> CombatSystemCore::CreateCombatAI(Player* player, CombatScenarioType scenario)
{
    if (!player)
        return nullptr;

    // 暂时只创建基础CombatAI，避免编译错误
    return std::make_unique<CombatAI>(player);
}

void CombatSystemCore::CleanupInvalidAIs()
{
    std::vector<ObjectGuid> toRemove;

    for (auto& pair : _combatAIs)
    {
        Player* player = ObjectAccessor::FindPlayer(pair.first);
        if (!player || !player->IsInWorld())
        {
            toRemove.push_back(pair.first);
        }
    }

    for (const ObjectGuid& guid : toRemove)
    {
        _combatAIs.erase(guid);
        _combatStats.erase(guid);
    }
}

void CombatSystemCore::HandlePlayerAttackCommand(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    // 确保机器人有战斗AI
    CombatAI* combatAI = GetCombatAI(bot);
    if (!combatAI)
    {
        // 自动注册战斗AI
        CombatScenarioType scenario = DetectCombatScenario(bot);
        RegisterCombatAI(bot, scenario);
        combatAI = GetCombatAI(bot);
    }

    if (combatAI)
    {
        // 验证目标有效性
        if (IsValidCombatTarget(bot, target))
        {
            combatAI->EnterCombat(target);
            LOG_DEBUG("combat", "机器人 {} 接到攻击指令，目标: {}",
                     bot->GetName(), target->GetName());
        }
        else
        {
            LOG_WARN("combat", "机器人 {} 收到无效攻击目标: {}",
                    bot->GetName(), target->GetName());
        }
    }
}

void CombatSystemCore::HandlePlayerStopAttackCommand(Player* bot)
{
    if (!bot)
        return;

    CombatAI* combatAI = GetCombatAI(bot);
    if (combatAI)
    {
        combatAI->LeaveCombat();
        LOG_DEBUG("combat", "机器人 {} 接到停止攻击指令", bot->GetName());
    }
}

void CombatSystemCore::HandlePlayerFollowCommand(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    CombatAI* combatAI = GetCombatAI(bot);
    if (combatAI)
    {
        // 设置跟随目标，但不进入战斗状态
        combatAI->MoveToTarget(target);
        LOG_DEBUG("combat", "机器人 {} 接到跟随指令，目标: {}",
                 bot->GetName(), target->GetName());
    }
}

void CombatSystemCore::HandlePlayerDefendCommand(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    CombatAI* combatAI = GetCombatAI(bot);
    if (combatAI)
    {
        // 设置防御目标，当目标受到攻击时自动反击
        // 这里可以设置一个防御模式标志
        LOG_DEBUG("combat", "机器人 {} 接到防御指令，保护目标: {}",
                 bot->GetName(), target->GetName());
    }
}
