#include "BotOpcodeHandler.h"
#include "BotEventSystem.h"
#include "PlayerPatch.h"
#include "Log.h"
#include "Player.h"
#include "WorldSession.h"
#include "WorldPacket.h"
#include "Opcodes.h"
#include "ObjectAccessor.h"
#include "Guild.h"
#include "GuildMgr.h"
#include "ArenaTeam.h"
#include "ArenaTeamMgr.h"
#include "Group.h"
#include "GroupMgr.h"
#include "PetitionMgr.h"
#include "ScriptMgr.h"
#include "World.h"
#include "TradeData.h"
#include "Util.h"

// 交互操作码拦截脚本
class BotInteractionOpcodeScript : public ServerScript
{
public:
    BotInteractionOpcodeScript() : ServerScript("BotInteractionOpcodeScript")
    {
        LOG_INFO("server", "🔧 [决斗调试] 初始化机器人交互操作码拦截器...");
    }

    // 拦截发送给玩家的数据包
    bool CanPacketSend(WorldSession* session, WorldPacket& packet) override
    {
        if (!session || !session->GetPlayer())
            return true;

        Player* player = session->GetPlayer();
        
        // 只处理机器人
        if (!PlayerPatch::GetIsFaker(player))
            return true;

        uint16 opcode = packet.GetOpcode();

        // 添加调试日志来监控所有发送给机器人的操作码
        if (opcode == SMSG_DUEL_REQUESTED)
        {
            LOG_INFO("server", "🔧 [决斗调试] 机器人 {} 收到决斗相关数据包，操作码: 0x{:04X}",
                player->GetName(), opcode);
        }
        else
        {
            LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到服务器数据包，操作码: 0x{:04X}",
                player->GetName(), opcode);
        }

        switch (opcode)
        {
            case SMSG_GUILD_INVITE:
                HandleGuildInvitePacket(player, packet);
                break;
            case SMSG_GROUP_INVITE:
                HandleGroupInvitePacket(player, packet);
                break;
            case SMSG_ARENA_TEAM_INVITE:
                HandleArenaTeamInvitePacket(player, packet);
                break;
            case SMSG_TRADE_STATUS:
                HandleTradeStatusPacket(player, packet);
                break;

            // case SMSG_DUEL_REQUESTED:
            //     LOG_INFO("server", "BotInteractionOpcodeScript: 拦截到决斗请求操作码 SMSG_DUEL_REQUESTED (0x{:04X})", opcode);
            //     HandleDuelRequestPacket(player, packet);
            //     break;
            case SMSG_BATTLEFIELD_MGR_ENTRY_INVITE:
                HandleBattlefieldInvitePacket(player, packet);
                break;
            default:
                break;
        }

        return true; // 继续正常处理
    }

    // 拦截玩家发送的数据包
    bool CanPacketReceive(WorldSession* session, WorldPacket& packet) override
    {
        if (!session || !session->GetPlayer())
            return true;

        Player* player = session->GetPlayer();
        uint16 opcode = packet.GetOpcode();

        // 添加调试日志来查看所有操作码
        if (PlayerPatch::GetIsFaker(player) ||
            (ObjectAccessor::FindPlayerByName(player->GetName(), false) &&
             ObjectAccessor::FindPlayerByName(player->GetName(), false)->GetSession() &&
             ObjectAccessor::FindPlayerByName(player->GetName(), false)->GetSession()->GetPlayer()))
        {
            LOG_DEBUG("server", "BotInteractionOpcodeScript: 收到操作码 0x{:04X} 来自玩家 {}", opcode, player->GetName());
        }

        // 处理章程签名请求
        if (opcode == CMSG_OFFER_PETITION)
        {
            LOG_INFO("server", "BotInteractionOpcodeScript: 检测到章程签名操作码 CMSG_OFFER_PETITION");
            HandlePetitionOfferPacket(player, packet);
        }
        // 处理公会邀请
        else if (opcode == CMSG_GUILD_INVITE)
        {
            HandleGuildInviteRequest(player, packet);
        }
        // 处理组队邀请
        else if (opcode == CMSG_GROUP_INVITE)
        {
            HandleGroupInviteRequest(player, packet);
        }
        // 处理竞技场战队邀请
        else if (opcode == CMSG_ARENA_TEAM_INVITE)
        {
            HandleArenaTeamInviteRequest(player, packet);
        }
        // 处理交易请求
        else if (opcode == CMSG_INITIATE_TRADE)
        {
            HandleTradeInitiateRequest(player, packet);
        }
        // 处理交易确认
        else if (opcode == CMSG_ACCEPT_TRADE)
        {
            HandleTradeAccept(player, packet);
        }


        return true; // 继续正常处理
    }

private:
    // 处理公会邀请数据包
    void HandleGuildInvitePacket(Player* bot, WorldPacket& packet)
    {
        LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到公会邀请数据包", bot->GetName());
        
        // 保存当前读取位置
        uint32 currentPos = packet.rpos();
        
        try
        {
            std::string inviterName, guildName;
            packet >> inviterName >> guildName;
            
            // 恢复读取位置
            packet.rpos(currentPos);
            
            // 查找邀请者
            Player* inviter = ObjectAccessor::FindPlayerByName(inviterName, false);
            
            // 触发公会邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_GUILD_INVITE, bot, inviter, guildName);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理公会邀请数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理组队邀请数据包
    void HandleGroupInvitePacket(Player* bot, WorldPacket& packet)
    {
        LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到组队邀请数据包", bot->GetName());
        
        // 保存当前读取位置
        uint32 currentPos = packet.rpos();
        
        try
        {
            std::string inviterName;
            packet >> inviterName;
            
            // 恢复读取位置
            packet.rpos(currentPos);
            
            // 查找邀请者
            Player* inviter = ObjectAccessor::FindPlayerByName(inviterName, false);
            
            // 触发组队邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_GROUP_INVITE, bot, inviter);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理组队邀请数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理竞技场战队邀请数据包
    void HandleArenaTeamInvitePacket(Player* bot, WorldPacket& packet)
    {
        LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到竞技场战队邀请数据包", bot->GetName());
        
        // 保存当前读取位置
        uint32 currentPos = packet.rpos();
        
        try
        {
            std::string inviterName, teamName;
            packet >> inviterName >> teamName;
            
            // 恢复读取位置
            packet.rpos(currentPos);
            
            // 查找邀请者
            Player* inviter = ObjectAccessor::FindPlayerByName(inviterName, false);
            
            // 触发竞技场战队邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_ARENA_TEAM_INVITE, bot, inviter, teamName);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理竞技场战队邀请数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理交易状态数据包
    void HandleTradeStatusPacket(Player* bot, WorldPacket& packet)
    {
        LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到交易状态数据包", bot->GetName());
        
        // 保存当前读取位置
        uint32 currentPos = packet.rpos();
        
        try
        {
            uint32 status;
            packet >> status;
            
            // 恢复读取位置
            packet.rpos(currentPos);
            
            // 根据交易状态触发相应事件
            if (status == TRADE_STATUS_BEGIN_TRADE)
            {
                // 读取交易者GUID
                ObjectGuid traderGuid;
                packet >> status >> traderGuid; // 重新读取状态和GUID
                packet.rpos(currentPos); // 恢复位置
                
                Player* trader = ObjectAccessor::FindPlayer(traderGuid);
                sBotEventMgr->TriggerTradeEvent(BotEventType::BOT_EVENT_TRADE_REQUEST, bot, trader, status);
            }
            else if (status == TRADE_STATUS_OPEN_WINDOW)
            {
                sBotEventMgr->TriggerTradeEvent(BotEventType::BOT_EVENT_TRADE_BEGIN, bot, nullptr, status);
            }
            else if (status == TRADE_STATUS_TRADE_ACCEPT)
            {
                sBotEventMgr->TriggerTradeEvent(BotEventType::BOT_EVENT_TRADE_ACCEPT, bot, nullptr, status);
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理交易状态数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理决斗请求数据包 (SMSG_DUEL_REQUESTED)
    void HandleDuelRequestPacket(Player* bot, WorldPacket& packet)
    {
        if (!bot || !bot->IsInWorld())
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: HandleDuelRequestPacket: 机器人参数无效");
            return;
        }

        LOG_INFO("server", "BotInteractionOpcodeScript: 机器人 {} 收到决斗请求数据包", bot->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            // 读取决斗仲裁者和请求者的GUID
            ObjectGuid arbiterGuid;
            ObjectGuid requesterGuid;
            packet >> arbiterGuid >> requesterGuid;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找请求者
            Player* requester = ObjectAccessor::FindPlayer(requesterGuid);
            if (!requester)
            {
                LOG_ERROR("server", "BotInteractionOpcodeScript: 找不到决斗请求者");
                return;
            }

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 向机器人 {} 发起决斗请求",
                requester->GetName(), bot->GetName());

            // 直接处理决斗响应，不通过事件系统
            WorldSession* session = bot->GetSession();
            if (session && bot->duel && bot->duel->Initiator)
            {
                // 90% 概率接受决斗
                if (urand(1, 100) <= 90)
                {
                    // 接受决斗
                    WorldPacket acceptPacket(CMSG_DUEL_ACCEPTED, 8);
                    acceptPacket << bot->GetGuidValue(PLAYER_DUEL_ARBITER);
                    session->HandleDuelAcceptedOpcode(acceptPacket);
                    LOG_INFO("server", "BotInteractionOpcodeScript: 机器人 {} 接受了决斗请求", bot->GetName());
                }
                else
                {
                    // 拒绝决斗
                    WorldPacket cancelPacket(CMSG_DUEL_CANCELLED, 8);
                    cancelPacket << bot->GetGuidValue(PLAYER_DUEL_ARBITER);
                    session->HandleDuelCancelledOpcode(cancelPacket);
                    LOG_INFO("server", "BotInteractionOpcodeScript: 机器人 {} 拒绝了决斗请求", bot->GetName());
                }
            }
            else
            {
                LOG_WARN("server", "BotInteractionOpcodeScript: 机器人 {} 没有有效的决斗状态", bot->GetName());
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理决斗请求数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理战场邀请数据包
    void HandleBattlefieldInvitePacket(Player* bot, WorldPacket& packet)
    {
        LOG_DEBUG("server", "BotInteractionOpcodeScript: 机器人 {} 收到战场邀请数据包", bot->GetName());
        
        // 保存当前读取位置
        uint32 currentPos = packet.rpos();
        
        try
        {
            uint32 battleId, zoneId, time;
            packet >> battleId >> zoneId >> time;
            
            // 恢复读取位置
            packet.rpos(currentPos);
            
            // 触发战场邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_BATTLEFIELD_INVITE, bot, nullptr, "", battleId);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理战场邀请数据包异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理章程签名请求
    void HandlePetitionOfferPacket(Player* requester, WorldPacket& packet)
    {
        if (!requester || !requester->IsInWorld())
        {
            LOG_WARN("server", "BotInteractionOpcodeScript: HandlePetitionOfferPacket - 无效的请求者");
            return;
        }

        LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 发送章程签名请求", requester->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            uint32 junk;
            ObjectGuid petitionGuid, targetGuid;
            packet >> junk >> petitionGuid >> targetGuid;

            LOG_INFO("server", "BotInteractionOpcodeScript: 章程GUID: {}, 目标GUID: {}",
                petitionGuid.ToString(), targetGuid.ToString());

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家 - 使用标准方法
            Player* targetPlayer = ObjectAccessor::FindConnectedPlayer(targetGuid);
            if (!targetPlayer)
            {
                // 尝试通过低位GUID查找
                targetPlayer = ObjectAccessor::FindPlayerByLowGUID(targetGuid.GetCounter());
            }

            if (!targetPlayer)
            {
                LOG_WARN("server", "BotInteractionOpcodeScript: 无法找到目标玩家 {}", targetGuid.ToString());

                // 调试：列出所有在线机器人的GUID
                LOG_INFO("server", "BotInteractionOpcodeScript: 当前在线机器人列表:");
                HashMapHolder<Player>::MapType const& players = ObjectAccessor::GetPlayers();
                int botCount = 0;
                for (auto const& pair : players)
                {
                    Player* player = pair.second;
                    if (player)
                    {
                        bool isFaker = PlayerPatch::GetIsFaker(player);
                        LOG_INFO("server", "  玩家: {} GUID: {} 是否机器人: {}",
                            player->GetName(), player->GetGUID().ToString(), isFaker ? "是" : "否");
                        if (isFaker)
                        {
                            botCount++;
                        }
                    }
                }
                LOG_INFO("server", "BotInteractionOpcodeScript: 总共找到 {} 个机器人", botCount);
                return;
            }

            if (!PlayerPatch::GetIsFaker(targetPlayer))
            {
                LOG_DEBUG("server", "BotInteractionOpcodeScript: 目标玩家 {} 不是机器人", targetPlayer->GetName());
                return;
            }

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 请求机器人 {} 签名章程",
                requester->GetName(), targetPlayer->GetName());

            // 获取章程信息
            Petition const* petition = sPetitionMgr->GetPetition(petitionGuid);
            if (!petition)
            {
                LOG_WARN("server", "BotInteractionOpcodeScript: 无法找到章程 {}", petitionGuid.ToString());
                return;
            }

            LOG_INFO("server", "BotInteractionOpcodeScript: 找到章程，类型: {}", petition->petitionType);

            // 根据章程类型触发相应事件 (简化判断)
            if (petition->petitionType == 9) // 公会章程类型通常是9
            {
                LOG_INFO("server", "BotInteractionOpcodeScript: 触发公会章程签名事件");
                sBotEventMgr->TriggerPetitionEvent(BotEventType::BOT_EVENT_GUILD_CHARTER, targetPlayer, requester, petitionGuid);
            }
            else
            {
                LOG_INFO("server", "BotInteractionOpcodeScript: 触发竞技场章程签名事件");
                sBotEventMgr->TriggerPetitionEvent(BotEventType::BOT_EVENT_ARENA_CHARTER, targetPlayer, requester, petitionGuid);
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理章程签名请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理公会邀请请求
    void HandleGuildInviteRequest(Player* inviter, WorldPacket& packet)
    {
        if (!inviter || !inviter->IsInWorld())
            return;

        LOG_DEBUG("server", "BotInteractionOpcodeScript: 玩家 {} 发送公会邀请", inviter->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            std::string targetName;
            packet >> targetName;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家
            Player* targetPlayer = ObjectAccessor::FindPlayerByName(targetName, false);
            if (!targetPlayer || !PlayerPatch::GetIsFaker(targetPlayer))
                return;

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 邀请机器人 {} 加入公会",
                inviter->GetName(), targetPlayer->GetName());

            // 直接触发公会邀请事件
            Guild* guild = inviter->GetGuild();
            std::string guildName = guild ? guild->GetName() : "";
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_GUILD_INVITE, targetPlayer, inviter, guildName);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理公会邀请请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理组队邀请请求
    void HandleGroupInviteRequest(Player* inviter, WorldPacket& packet)
    {
        if (!inviter || !inviter->IsInWorld())
            return;

        LOG_DEBUG("server", "BotInteractionOpcodeScript: 玩家 {} 发送组队邀请", inviter->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            std::string targetName;
            packet >> targetName;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家
            Player* targetPlayer = ObjectAccessor::FindPlayerByName(targetName, false);
            if (!targetPlayer || !PlayerPatch::GetIsFaker(targetPlayer))
                return;

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 邀请机器人 {} 组队",
                inviter->GetName(), targetPlayer->GetName());

            // 直接触发组队邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_GROUP_INVITE, targetPlayer, inviter);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理组队邀请请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理竞技场战队邀请请求
    void HandleArenaTeamInviteRequest(Player* inviter, WorldPacket& packet)
    {
        if (!inviter || !inviter->IsInWorld())
            return;

        LOG_DEBUG("server", "BotInteractionOpcodeScript: 玩家 {} 发送竞技场战队邀请", inviter->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            uint32 arenaTeamId;
            std::string targetName;
            packet >> arenaTeamId >> targetName;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家
            Player* targetPlayer = ObjectAccessor::FindPlayerByName(targetName, false);
            if (!targetPlayer || !PlayerPatch::GetIsFaker(targetPlayer))
                return;

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 邀请机器人 {} 加入竞技场战队",
                inviter->GetName(), targetPlayer->GetName());

            // 获取竞技场战队信息
            ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
            std::string teamName = arenaTeam ? arenaTeam->GetName() : "";

            // 直接触发竞技场战队邀请事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_ARENA_TEAM_INVITE, targetPlayer, inviter, teamName, arenaTeamId);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理竞技场战队邀请请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理交易发起请求
    void HandleTradeInitiateRequest(Player* initiator, WorldPacket& packet)
    {
        if (!initiator || !initiator->IsInWorld())
            return;

        LOG_DEBUG("server", "BotInteractionOpcodeScript: 玩家 {} 发起交易", initiator->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            ObjectGuid targetGuid;
            packet >> targetGuid;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家
            Player* targetPlayer = ObjectAccessor::FindPlayer(targetGuid);
            if (!targetPlayer || !PlayerPatch::GetIsFaker(targetPlayer))
                return;

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 向机器人 {} 发起交易",
                initiator->GetName(), targetPlayer->GetName());

            // 直接触发交易请求事件
            sBotEventMgr->TriggerTradeEvent(BotEventType::BOT_EVENT_TRADE_REQUEST, targetPlayer, initiator, TRADE_STATUS_BEGIN_TRADE);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理交易发起请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理决斗提议请求
    void HandleDuelProposeRequest(Player* proposer, WorldPacket& packet)
    {
        if (!proposer || !proposer->IsInWorld())
            return;

        LOG_DEBUG("server", "BotInteractionOpcodeScript: 玩家 {} 提议决斗", proposer->GetName());

        // 保存当前读取位置
        uint32 currentPos = packet.rpos();

        try
        {
            ObjectGuid targetGuid;
            packet >> targetGuid;

            // 恢复读取位置
            packet.rpos(currentPos);

            // 查找目标玩家
            Player* targetPlayer = ObjectAccessor::FindPlayer(targetGuid);
            if (!targetPlayer || !PlayerPatch::GetIsFaker(targetPlayer))
                return;

            LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 向机器人 {} 提议决斗",
                proposer->GetName(), targetPlayer->GetName());

            // 直接触发决斗请求事件
            sBotEventMgr->TriggerInteractionEvent(BotEventType::BOT_EVENT_DUEL_REQUEST, targetPlayer, proposer);
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("server", "BotInteractionOpcodeScript: 处理决斗提议请求异常: {}", e.what());
            packet.rpos(currentPos);
        }
    }

    // 处理交易确认
    void HandleTradeAccept(Player* player, WorldPacket& packet)
    {
        if (!player || !player->IsInWorld())
            return;

        // 检查玩家是否在交易中
        TradeData* tradeData = player->GetTradeData();
        if (!tradeData)
            return;

        // 获取交易对象
        Player* trader = tradeData->GetTrader();
        if (!trader || !PlayerPatch::GetIsFaker(trader))
            return;

        LOG_INFO("server", "BotInteractionOpcodeScript: 玩家 {} 确认交易，机器人 {} 收到确认",
            player->GetName(), trader->GetName());

        // 触发交易确认事件
        sBotEventMgr->TriggerTradeEvent(BotEventType::BOT_EVENT_TRADE_ACCEPT, trader, player);
    }
};

// 注册脚本
void AddSC_BotInteractionOpcodeHandler()
{
    new BotInteractionOpcodeScript();
}
