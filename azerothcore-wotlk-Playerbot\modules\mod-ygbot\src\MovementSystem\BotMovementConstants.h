#ifndef BOT_MOVEMENT_CONSTANTS_H
#define BOT_MOVEMENT_CONSTANTS_H

#include "Define.h"
#include <algorithm>
#include <cmath>

// ========================================
// 机器人移动系统常量定义
// 用于防止飞天遁地和确保符合AzerothCore移动规则
// ========================================

// 基础移动常量
constexpr float BOT_MOVEMENT_INVALID_HEIGHT = -500000.0f;    // 无效高度标识
constexpr float BOT_MOVEMENT_MAX_HEIGHT = 2000.0f;          // 最大查询高度
constexpr float BOT_MOVEMENT_MIN_HEIGHT = -1000.0f;         // 最小允许高度

// 地形验证常量 - 防止飞天遁地的核心参数
constexpr float BOT_MOVEMENT_GROUND_TOLERANCE = 2.0f;       // 地面容差 (2码)
constexpr float BOT_MOVEMENT_WATER_TOLERANCE = 1.0f;        // 水面容差 (1码)
constexpr float BOT_MOVEMENT_MAX_STEP_HEIGHT = 2.5f;        // 最大台阶高度
constexpr float BOT_MOVEMENT_MAX_FALL_HEIGHT = 50.0f;       // 最大掉落高度
constexpr float BOT_MOVEMENT_UNDERGROUND_LIMIT = 5.0f;      // 地下限制深度

// 坡度和角度限制
constexpr float BOT_MOVEMENT_MAX_SLOPE_ANGLE = 50.0f;       // 最大可行走坡度角度(度)
constexpr float BOT_MOVEMENT_MAX_CLIMB_ANGLE = 60.0f;       // 最大攀爬角度(度)

// 移动距离限制
constexpr float BOT_MOVEMENT_MIN_DISTANCE = 0.5f;          // 最小移动距离
constexpr float BOT_MOVEMENT_MAX_DISTANCE = 100.0f;        // 最大移动距离
constexpr float BOT_MOVEMENT_STUCK_DISTANCE = 1.0f;        // 卡住判定距离

// 速度限制 (防止异常移动)
constexpr float BOT_MOVEMENT_MIN_SPEED = 0.5f;             // 最小移动速度
constexpr float BOT_MOVEMENT_MAX_SPEED = 20.0f;            // 最大移动速度
constexpr float BOT_MOVEMENT_DEFAULT_SPEED = 7.0f;         // 默认移动速度

// 时间相关常量
constexpr uint32 BOT_MOVEMENT_UPDATE_INTERVAL = 1000;      // 更新间隔(毫秒)
constexpr uint32 BOT_MOVEMENT_STUCK_TIME = 5000;           // 卡住检测时间(毫秒)
constexpr uint32 BOT_MOVEMENT_TIMEOUT = 30000;             // 移动超时时间(毫秒)

// 路径验证常量
constexpr uint32 BOT_MOVEMENT_MAX_PATH_POINTS = 50;        // 最大路径点数
constexpr float BOT_MOVEMENT_PATH_STEP_SIZE = 2.0f;        // 路径步长
constexpr uint32 BOT_MOVEMENT_MAX_PATH_CHECKS = 10;        // 最大路径检查次数
constexpr uint32 BOT_MOVEMENT_PATH_CHECK_POINTS = 10;      // 路径检查点数

// 坐标范围限制 (基于魔兽世界地图范围)
constexpr float BOT_MOVEMENT_WORLD_MIN_X = -20000.0f;      // 世界最小X坐标
constexpr float BOT_MOVEMENT_WORLD_MAX_X = 20000.0f;       // 世界最大X坐标
constexpr float BOT_MOVEMENT_WORLD_MIN_Y = -20000.0f;      // 世界最小Y坐标
constexpr float BOT_MOVEMENT_WORLD_MAX_Y = 20000.0f;       // 世界最大Y坐标

// 特殊移动模式常量
constexpr float BOT_MOVEMENT_FLY_HEIGHT_MIN = 3.0f;        // 最小飞行高度
constexpr float BOT_MOVEMENT_FLY_HEIGHT_MAX = 100.0f;      // 最大飞行高度
constexpr float BOT_MOVEMENT_SWIM_DEPTH_MAX = 50.0f;       // 最大游泳深度

// 碰撞检测常量
constexpr float BOT_MOVEMENT_COLLISION_RADIUS = 1.0f;      // 碰撞检测半径
constexpr uint32 BOT_MOVEMENT_COLLISION_CHECKS = 5;        // 碰撞检测点数

// 安全检查常量
constexpr uint32 BOT_MOVEMENT_SAFETY_CHECK_INTERVAL = 500; // 安全检查间隔(毫秒)
constexpr uint32 BOT_MOVEMENT_MAX_CORRECTIONS = 3;         // 最大位置修正次数
constexpr float BOT_MOVEMENT_CORRECTION_RADIUS = 5.0f;     // 位置修正搜索半径

// 移动标志位 (基于AzerothCore MovementFlags)
enum BotMovementFlags : uint32
{
    BOT_MOVEFLAG_NONE               = 0x00000000,
    BOT_MOVEFLAG_FORWARD            = 0x00000001,
    BOT_MOVEFLAG_BACKWARD           = 0x00000002,
    BOT_MOVEFLAG_STRAFE_LEFT        = 0x00000004,
    BOT_MOVEFLAG_STRAFE_RIGHT       = 0x00000008,
    BOT_MOVEFLAG_TURN_LEFT          = 0x00000010,
    BOT_MOVEFLAG_TURN_RIGHT         = 0x00000020,
    BOT_MOVEFLAG_PITCH_UP           = 0x00000040,
    BOT_MOVEFLAG_PITCH_DOWN         = 0x00000080,
    BOT_MOVEFLAG_WALKING            = 0x00000100,
    BOT_MOVEFLAG_ONTRANSPORT        = 0x00000200,
    BOT_MOVEFLAG_DISABLE_GRAVITY    = 0x00000400,
    BOT_MOVEFLAG_ROOT               = 0x00000800,
    BOT_MOVEFLAG_FALLING            = 0x00001000,
    BOT_MOVEFLAG_FALLING_FAR        = 0x00002000,
    BOT_MOVEFLAG_PENDING_STOP       = 0x00004000,
    BOT_MOVEFLAG_PENDING_STRAFE_STOP = 0x00008000,
    BOT_MOVEFLAG_PENDING_FORWARD    = 0x00010000,
    BOT_MOVEFLAG_PENDING_BACKWARD   = 0x00020000,
    BOT_MOVEFLAG_PENDING_STRAFE_LEFT = 0x00040000,
    BOT_MOVEFLAG_PENDING_STRAFE_RIGHT = 0x00080000,
    BOT_MOVEFLAG_PENDING_ROOT       = 0x00100000,
    BOT_MOVEFLAG_SWIMMING           = 0x00200000,
    BOT_MOVEFLAG_ASCENDING          = 0x00400000,
    BOT_MOVEFLAG_DESCENDING         = 0x00800000,
    BOT_MOVEFLAG_CAN_FLY            = 0x01000000,
    BOT_MOVEFLAG_FLYING             = 0x02000000,
    BOT_MOVEFLAG_SPLINE_ELEVATION   = 0x04000000,
    BOT_MOVEFLAG_SPLINE_ENABLED     = 0x08000000,
    BOT_MOVEFLAG_WATERWALKING       = 0x10000000,
    BOT_MOVEFLAG_FALLING_SLOW       = 0x20000000,
    BOT_MOVEFLAG_HOVER              = 0x40000000
};

// 地形类型枚举
enum TerrainType
{
    TERRAIN_INVALID = 0,        // 无效地形
    TERRAIN_GROUND,             // 地面
    TERRAIN_WATER,              // 水面
    TERRAIN_AIR,                // 空中
    TERRAIN_UNDERGROUND,        // 地下
    TERRAIN_UNKNOWN             // 未知
};

// 移动验证结果
enum MovementValidationResult
{
    MOVEMENT_VALID = 0,          // 有效
    MOVEMENT_INVALID_POSITION,   // 无效位置
    MOVEMENT_OUT_OF_BOUNDS,      // 超出边界
    MOVEMENT_UNDERGROUND,        // 地下
    MOVEMENT_TOO_HIGH,           // 过高
    MOVEMENT_BLOCKED,            // 被阻挡
    MOVEMENT_STEEP_SLOPE,        // 坡度过陡
    MOVEMENT_NO_PATH             // 无路径
};

// 调试和日志级别
enum BotMovementLogLevel
{
    LOG_NONE = 0,           // 无日志
    LOG_ERROR,              // 错误
    LOG_WARNING,            // 警告
    LOG_INFO,               // 信息
    LOG_DEBUG,              // 调试
    LOG_VERBOSE             // 详细
};

// 宏定义 - 便于调试和配置
#define BOT_MOVEMENT_ENABLE_LOGGING 1
#define BOT_MOVEMENT_ENABLE_SAFETY_CHECKS 1
#define BOT_MOVEMENT_ENABLE_TERRAIN_VALIDATION 1
#define BOT_MOVEMENT_ENABLE_ANTI_CHEAT 1

// 内联函数 - 常用计算
inline bool IsValidCoordinate(float x, float y)
{
    return (x >= BOT_MOVEMENT_WORLD_MIN_X && x <= BOT_MOVEMENT_WORLD_MAX_X &&
            y >= BOT_MOVEMENT_WORLD_MIN_Y && y <= BOT_MOVEMENT_WORLD_MAX_Y);
}

inline bool IsValidHeight(float z)
{
    return (z >= BOT_MOVEMENT_MIN_HEIGHT && z <= BOT_MOVEMENT_MAX_HEIGHT);
}

inline float ClampSpeed(float speed)
{
    return std::max(BOT_MOVEMENT_MIN_SPEED, std::min(speed, BOT_MOVEMENT_MAX_SPEED));
}

inline float CalculateDistance2D(float x1, float y1, float x2, float y2)
{
    float dx = x2 - x1;
    float dy = y2 - y1;
    return std::sqrt(dx * dx + dy * dy);
}

inline float CalculateDistance3D(float x1, float y1, float z1, float x2, float y2, float z2)
{
    float dx = x2 - x1;
    float dy = y2 - y1;
    float dz = z2 - z1;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

#endif // BOT_MOVEMENT_CONSTANTS_H
