#include "CombatSystemCore.h"
#include "CombatAI.h"
#include "ScriptMgr.h"
#include "Player.h"
#include "Log.h"
#include "../PlayerPatch.h"

// 战斗系统世界脚本
class CombatSystemWorldScript : public WorldScript
{
public:
    CombatSystemWorldScript() : WorldScript("CombatSystemWorldScript") {}

    void OnAfterConfigLoad(bool /*reload*/) override
    {
        // 初始化战斗系统
        sCombatSystem->Initialize();
        LOG_INFO("server.loading", "战斗系统已初始化 - 自动战斗模式");
    }

    void OnShutdown() override
    {
        // 关闭战斗系统
        sCombatSystem->Shutdown();
        LOG_INFO("server.loading", "战斗系统已关闭");
    }

    void OnUpdate(uint32 diff) override
    {
        // 更新战斗系统
        sCombatSystem->Update(diff);
    }
};

// 战斗系统玩家脚本
class CombatSystemPlayerScript : public PlayerScript
{
public:
    CombatSystemPlayerScript() : PlayerScript("CombatSystemPlayerScript") {}

    void OnPlayerLogin(Player* player) override
    {
        if (!player || !PlayerPatch::GetIsFaker(player))
            return;

        // 为机器人自动注册战斗AI
        CombatScenarioType scenario = sCombatSystem->DetectCombatScenario(player);
        sCombatSystem->RegisterCombatAI(player, scenario);
        LOG_DEBUG("combat", "机器人 {} 自动启用战斗AI (场景: {})",
                 player->GetName(), static_cast<uint32>(scenario));
    }

    void OnPlayerLogout(Player* player) override
    {
        if (!player || !PlayerPatch::GetIsFaker(player))
            return;

        // 移除战斗AI
        sCombatSystem->UnregisterCombatAI(player);
        LOG_DEBUG("combat", "移除机器人 {} 的战斗AI", player->GetName());
    }




};



// 注册脚本
void AddSC_CombatSystem()
{
    new CombatSystemWorldScript();
    new CombatSystemPlayerScript();
}

// 自动注册
class CombatSystemLoader
{
public:
    CombatSystemLoader()
    {
        AddSC_CombatSystem();
        LOG_INFO("server.loading", "战斗系统脚本已自动注册 - 支持自动战斗响应");
    }
};

static CombatSystemLoader _combatSystemLoader;
