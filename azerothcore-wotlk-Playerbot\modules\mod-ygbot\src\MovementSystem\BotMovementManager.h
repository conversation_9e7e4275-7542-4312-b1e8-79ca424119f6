#ifndef BOT_MOVEMENT_MANAGER_H
#define BOT_MOVEMENT_MANAGER_H

#include "BotMovementConstants.h"
#include "BotTerrainValidator.h"
#include "Define.h"
#include "Common.h"
#include <unordered_map>
#include <vector>
#include <memory>
#include <queue>

// 前向声明
class Player;
class Unit;
class Creature;
class GameObject;
class WorldLocation;

// ========================================
// 移动类型枚举
// ========================================
enum BotMovementType
{
    BOT_MOVEMENT_IDLE = 0,           // 空闲状态
    BOT_MOVEMENT_RANDOM_WALK,        // 随机漫步
    BOT_MOVEMENT_FOLLOW_TARGET,      // 跟随目标
    BOT_MOVEMENT_PATROL,             // 巡逻路线
    BOT_MOVEMENT_EXPLORE,            // 探索移动
    BOT_MOVEMENT_COMBAT_POSITIONING, // 战斗定位
    BOT_MOVEMENT_FLEE,               // 逃跑
    BOT_MOVEMENT_RETURN_HOME,        // 返回出生点
    BOT_MOVEMENT_CUSTOM_PATH,        // 自定义路径
    BOT_MOVEMENT_FORMATION           // 编队移动
};

// 移动状态
enum BotMovementState
{
    BOT_STATE_STOPPED = 0,
    BOT_STATE_MOVING,
    BOT_STATE_PAUSED,
    BOT_STATE_INTERRUPTED,
    BOT_STATE_COMPLETED
};

// 移动优先级
enum BotMovementPriority
{
    BOT_PRIORITY_LOWEST = 0,
    BOT_PRIORITY_LOW,
    BOT_PRIORITY_NORMAL,
    BOT_PRIORITY_HIGH,
    BOT_PRIORITY_HIGHEST,
    BOT_PRIORITY_EMERGENCY
};

// ========================================
// 移动参数结构
// ========================================
struct BotMovementParams
{
    BotMovementType type;
    BotMovementPriority priority;
    float speed = 0.0f;                    // 移动速度 (0 = 使用默认)
    float minDistance = 2.0f;              // 最小距离
    float maxDistance = 30.0f;             // 最大距离
    uint32 duration = 0;                   // 持续时间 (0 = 无限制)
    bool canFly = false;                   // 是否可以飞行
    bool canSwim = true;                   // 是否可以游泳
    bool generatePath = true;              // 是否生成路径
    bool forcedMovement = false;           // 是否强制移动
    bool enableAntiCheat = true;           // 启用防作弊检查
    
    BotMovementParams() : type(BOT_MOVEMENT_IDLE), priority(BOT_PRIORITY_NORMAL) {}
    BotMovementParams(BotMovementType t, BotMovementPriority p = BOT_PRIORITY_NORMAL)
        : type(t), priority(p) {}
};

// ========================================
// 路径点结构
// ========================================
struct BotWaypoint
{
    float x, y, z;
    uint32 waitTime;        // 等待时间(毫秒)
    uint32 flags;           // 特殊标志
    
    BotWaypoint() : x(0.0f), y(0.0f), z(0.0f), waitTime(0), flags(0) {}
    BotWaypoint(float _x, float _y, float _z, uint32 _waitTime = 0, uint32 _flags = 0)
        : x(_x), y(_y), z(_z), waitTime(_waitTime), flags(_flags) {}
};

// ========================================
// 移动目标结构
// ========================================
struct BotMovementTarget
{
    float x, y, z;
    uint64 targetGuid;      // 目标GUID (如果跟随单位)
    uint32 flags;
    
    BotMovementTarget() : x(0.0f), y(0.0f), z(0.0f), targetGuid(0), flags(0) {}
    BotMovementTarget(float _x, float _y, float _z) : x(_x), y(_y), z(_z), targetGuid(0), flags(0) {}
    BotMovementTarget(uint64 _guid) : x(0.0f), y(0.0f), z(0.0f), targetGuid(_guid), flags(0) {}
};

// ========================================
// 移动任务基类
// ========================================
class BotMovementTask
{
public:
    BotMovementTask(uint32 id, BotMovementType type, const BotMovementParams& params)
        : taskId(id), movementType(type), parameters(params), state(BOT_STATE_STOPPED) {}
    
    virtual ~BotMovementTask() = default;
    
    // 执行移动任务
    virtual bool Execute(Player* bot) = 0;
    
    // 更新任务状态
    virtual bool Update(Player* bot, uint32 diff) = 0;
    
    // 停止任务
    virtual void Stop(Player* bot) = 0;
    
    // 暂停/恢复任务
    virtual void Pause() { state = BOT_STATE_PAUSED; }
    virtual void Resume() { if (state == BOT_STATE_PAUSED) state = BOT_STATE_MOVING; }
    
    // 获取任务信息
    uint32 GetTaskId() const { return taskId; }
    BotMovementType GetType() const { return movementType; }
    BotMovementState GetState() const { return state; }
    BotMovementPriority GetPriority() const { return parameters.priority; }
    
    // 检查任务是否完成
    bool IsCompleted() const { return state == BOT_STATE_COMPLETED; }
    bool IsActive() const { return state == BOT_STATE_MOVING; }
    
    // 安全检查
    virtual bool IsSafeToExecute(Player* bot) const;

protected:
    uint32 taskId;
    BotMovementType movementType;
    BotMovementParams parameters;
    BotMovementState state;
    uint32 startTime = 0;
    uint32 lastUpdateTime = 0;
};

// ========================================
// 机器人移动管理器
// 核心功能：管理机器人移动，防止飞天遁地
// ========================================
class BotMovementManager
{
public:
    // 单例模式
    static BotMovementManager& instance()
    {
        static BotMovementManager instance;
        return instance;
    }
    
    // 初始化移动系统
    void Initialize();
    
    // 更新机器人移动
    void UpdateBot(Player* bot, uint32 diff);
    
    // ========================================
    // 任务管理方法
    // ========================================
    
    // 添加移动任务
    uint32 AddMovementTask(Player* bot, std::shared_ptr<BotMovementTask> task);
    
    // 移除移动任务
    bool RemoveMovementTask(Player* bot, uint32 taskId);
    
    // 清除所有移动任务
    void ClearMovementTasks(Player* bot);
    
    // 暂停/恢复移动
    void PauseMovement(Player* bot);
    void ResumeMovement(Player* bot);
    
    // 强制停止移动
    void StopMovement(Player* bot);
    
    // 获取当前移动任务
    std::shared_ptr<BotMovementTask> GetCurrentTask(Player* bot);
    
    // 检查移动状态
    bool IsMoving(Player* bot);
    bool IsPaused(Player* bot);
    
    // ========================================
    // 便捷移动方法
    // ========================================
    
    // 移动到指定位置
    uint32 MoveToPosition(Player* bot, float x, float y, float z, const BotMovementParams& params = BotMovementParams());
    
    // 移动到目标
    uint32 MoveToTarget(Player* bot, Unit* target, const BotMovementParams& params = BotMovementParams());
    
    // 跟随目标
    uint32 FollowTarget(Player* bot, Unit* target, float distance = 5.0f);
    
    // 跟随组队领袖
    uint32 FollowGroupLeader(Player* bot, float distance = 4.0f);
    
    // 开始随机漫步
    uint32 StartRandomWalk(Player* bot, float radius = 20.0f);
    
    // 开始巡逻
    uint32 StartPatrol(Player* bot, const std::vector<BotWaypoint>& waypoints);
    
    // 逃离目标
    uint32 FleeFromTarget(Player* bot, Unit* target, float distance = 20.0f);
    
    // ========================================
    // 配置和设置方法
    // ========================================
    
    // 设置移动参数
    void SetDefaultSpeed(Player* bot, float speed);
    void SetMovementFlags(Player* bot, uint32 flags);
    
    // 启用/禁用防作弊检查
    void SetAntiCheatEnabled(bool enabled) { antiCheatEnabled = enabled; }
    bool IsAntiCheatEnabled() const { return antiCheatEnabled; }
    
    // 获取统计信息
    struct MovementStats
    {
        uint32 totalTasks = 0;
        uint32 completedTasks = 0;
        uint32 failedTasks = 0;
        uint32 antiCheatBlocks = 0;
        uint32 positionCorrections = 0;
    };
    
    const MovementStats& GetStats() const { return stats; }
    void ResetStats() { stats = MovementStats(); }

private:
    BotMovementManager() = default;
    
    // 机器人移动状态
    struct BotMovementState
    {
        std::queue<std::shared_ptr<BotMovementTask>> taskQueue;
        std::shared_ptr<BotMovementTask> currentTask;
        bool isPaused = false;
        float defaultSpeed = 0.0f;
        uint32 movementFlags = 0;
        uint32 completedTasks = 0;
        uint32 lastUpdateTime = 0;
        uint32 lastSafetyCheck = 0;
        PathPoint lastPosition;
        uint32 stuckCounter = 0;
    };
    
    std::unordered_map<uint64, BotMovementState> botStates;
    
    // 配置参数
    bool antiCheatEnabled = true;
    MovementStats stats;
    
    // ========================================
    // 内部方法
    // ========================================
    
    // 任务处理
    void ProcessTaskQueue(Player* bot, BotMovementState& state);
    bool CanExecuteTask(Player* bot, const BotMovementTask& task);
    void CleanupCompletedTasks(Player* bot, BotMovementState& state);
    
    // 安全检查
    bool PerformSafetyCheck(Player* bot, BotMovementState& state);
    bool ValidateMovement(Player* bot, float x, float y, float z);
    void HandleStuckBot(Player* bot, BotMovementState& state);
    
    // 禁止拷贝
    BotMovementManager(const BotMovementManager&) = delete;
    BotMovementManager& operator=(const BotMovementManager&) = delete;
};

// 全局访问宏
#define sBotMovementManager BotMovementManager::instance()

#endif // BOT_MOVEMENT_MANAGER_H
