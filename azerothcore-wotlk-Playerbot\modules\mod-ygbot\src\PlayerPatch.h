#ifndef PLAYER_PATCH_H
#define PLAYER_PATCH_H

#include "Player.h"

// 扩展Player类，添加IsFaker属性
namespace PlayerPatch
{
    // 使用一个全局的map来存储玩家的IsFaker状态
    static std::unordered_map<uint64, bool> playerFakerStatus;

    // 获取玩家的IsFaker状态
    inline bool GetIsFaker(Player* player)
    {
        if (!player)
            return false;
            
        auto it = playerFakerStatus.find(player->GetGUID().GetCounter());
        if (it != playerFakerStatus.end())
            return it->second;
            
        return false;
    }
    
    // 设置玩家的IsFaker状态
    inline void SetIsFaker(Player* player, bool value)
    {
        if (!player)
            return;
            
        playerFakerStatus[player->GetGUID().GetCounter()] = value;
    }
}

// 为Player类添加IsFaker属性的访问器
namespace acore
{
    class Player;
}

// 添加IsFaker属性访问器
#define IsFaker PlayerPatch::GetIsFaker(this)

#endif // PLAYER_PATCH_H 