#include "BotEventHandlers.h"
#include "BotEventSystem.h"
#include "PlayerPatch.h"
#include "Log.h"
#include "../CombatSystem/BotSkillManager.h"
#include "../CombatSystem/CombatSystemCore.h"
#include "../CombatSystem/CombatAI.h"
#include "World.h"
#include "Player.h"
#include "Unit.h"
#include "Creature.h"
#include "GameObject.h"
#include "ObjectMgr.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include "Cell.h"
#include "CellImpl.h"
#include "MotionMaster.h"
#include "BotDeathHandler.h"
#include "WorldSession.h"
#include "WorldPacket.h"
#include "Opcodes.h"
#include "Config.h"
#include "Guild.h"
#include "GuildMgr.h"
#include "ArenaTeam.h"
#include "ArenaTeamMgr.h"
#include "Group.h"
#include "GroupMgr.h"
#include "PetitionMgr.h"
#include "Util.h"
#include "TaskScheduler.h"
#include <random>

// 全局随机数生成器
static std::random_device rd;
static std::mt19937 gen(rd());

// 全局任务调度器 (用于交互响应)
TaskScheduler g_interactionScheduler;

// 静态处理器实例
std::vector<std::shared_ptr<IBotEventHandler>> BotEventHandlerFactory::handlers;

// BasicBotEventHandler 实现

bool BasicBotEventHandler::CanHandle(BotEventType eventType) const
{
    return eventType == BotEventType::BOT_EVENT_LOGIN ||
           eventType == BotEventType::BOT_EVENT_LOGOUT ||
           eventType == BotEventType::BOT_EVENT_UPDATE ||
           eventType == BotEventType::BOT_EVENT_LEVEL_UP;
}

bool BasicBotEventHandler::HandleEvent(const BotEventData& eventData)
{
    if (!eventData.bot || !PlayerPatch::GetIsFaker(eventData.bot))
        return false;

    switch (eventData.eventType)
    {
        case BotEventType::BOT_EVENT_LOGIN:
            HandleLoginEvent(eventData.bot);
            break;
        case BotEventType::BOT_EVENT_LOGOUT:
            HandleLogoutEvent(eventData.bot);
            break;
        case BotEventType::BOT_EVENT_UPDATE:
            HandleUpdateEvent(eventData.bot);
            break;
        case BotEventType::BOT_EVENT_LEVEL_UP:
            {
                // 尝试转换为升级事件数据
                if (eventData.eventType == BotEventType::BOT_EVENT_LEVEL_UP)
                {
                    // 从玩家当前等级获取新等级
                    uint32 newLevel = eventData.bot ? eventData.bot->GetLevel() : 1;
                    HandleLevelUpEvent(eventData.bot, newLevel);
                }
            }
            break;
        default:
            return false;
    }
    
    return true;
}

void BasicBotEventHandler::HandleLoginEvent(Player* bot)
{
    LOG_DEBUG("server", "BasicBotEventHandler: 机器人 {} 登录事件", bot->GetName());

    // 确保机器人状态正常
    if (!bot->IsAlive())
    {
        LOG_INFO("server", "机器人 {} 登录时死亡，需要复活", bot->GetName());
        // 简单复活处理
        bot->ResurrectPlayer(1.0f);
        bot->SpawnCorpseBones();
    }

    // 设置基本状态
    bot->SetHealth(bot->GetMaxHealth());
    bot->SetPower(POWER_MANA, bot->GetMaxPower(POWER_MANA));

    // 🎯 机器人登录时学习技能
    LOG_INFO("server", "机器人 {} 开始学习技能 (等级: {}, 职业: {})",
             bot->GetName(), bot->GetLevel(), bot->getClass());

    // 使用技能管理器学习技能
    sBotSkillManager->LearnSkillsOnLogin(bot);

    // 注册战斗AI并加载已学技能
    CombatAI* combatAI = sCombatSystem->GetCombatAI(bot);
    if (!combatAI)
    {
        // 自动注册战斗AI
        CombatScenarioType scenario = sCombatSystem->DetectCombatScenario(bot);
        sCombatSystem->RegisterCombatAI(bot, scenario);
        combatAI = sCombatSystem->GetCombatAI(bot);
    }

    if (combatAI)
    {
        // 加载已学技能到战斗AI
        combatAI->RefreshLearnedSpells();
        LOG_DEBUG("server", "机器人 {} 战斗AI技能加载完成", bot->GetName());
    }

    // 只有活着的机器人才触发移动事件
    if (bot->IsAlive())
    {
        LOG_DEBUG("server", "机器人 {} 活着，延迟触发移动事件", bot->GetName());
        // 延迟5秒后触发移动，让机器人完全初始化
        // TODO: 使用定时器延迟触发
        // sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_MOVE_START, bot);
    }
    else
    {
        LOG_DEBUG("server", "机器人 {} 死亡，不触发移动事件", bot->GetName());
    }
}

void BasicBotEventHandler::HandleLogoutEvent(Player* bot)
{
    LOG_DEBUG("server", "BasicBotEventHandler: 机器人 {} 登出事件", bot->GetName());
    
    // 清理机器人相关事件
    sBotEventMgr->CleanupBotEvents(bot);
}

void BasicBotEventHandler::HandleUpdateEvent(Player* bot)
{
    // 定期检查机器人状态
    if (!bot->IsAlive())
    {
        sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_RESURRECT, bot);
        return;
    }
    
    // 随机触发一些行为
    std::uniform_int_distribution<> dis(1, 100);
    int chance = dis(gen);
    
    if (chance <= 5) // 5% 概率发送随机聊天
    {
        sBotEventMgr->TriggerChatEvent(BotEventType::BOT_EVENT_CHAT_SAY, bot, "", CHAT_MSG_SAY, LANG_COMMON);
    }
    else if (chance <= 10) // 5% 概率发送随机表情
    {
        sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_EMOTE, bot);
    }
    else if (chance <= 30 && !bot->IsInCombat()) // 20% 概率移动（非战斗状态）
    {
        sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_MOVE_START, bot);
    }
}

void BasicBotEventHandler::HandleLevelUpEvent(Player* bot, uint32 newLevel)
{
    LOG_INFO("server", "BasicBotEventHandler: 机器人 {} 升级到 {} 级", bot->GetName(), newLevel);

    // 使用技能管理器学习新等级的技能
    sBotSkillManager->LearnSkillsOnLevelUp(bot, static_cast<uint8>(newLevel));

    // 更新战斗AI的技能列表
    CombatAI* combatAI = sCombatSystem->GetCombatAI(bot);
    if (combatAI)
    {
        combatAI->RefreshLearnedSpells();
        LOG_DEBUG("server", "机器人 {} 升级后技能列表已更新", bot->GetName());
    }

    // 恢复满血满蓝
    bot->SetHealth(bot->GetMaxHealth());
    bot->SetPower(POWER_MANA, bot->GetMaxPower(POWER_MANA));

    LOG_INFO("server", "机器人 {} 升级处理完成", bot->GetName());
}

// MovementEventHandler 实现

bool MovementEventHandler::CanHandle(BotEventType eventType) const
{
    return eventType == BotEventType::BOT_EVENT_MOVE_START ||
           eventType == BotEventType::BOT_EVENT_MOVE_STOP ||
           eventType == BotEventType::BOT_EVENT_TELEPORT ||
           eventType == BotEventType::BOT_EVENT_ZONE_CHANGE ||
           eventType == BotEventType::BOT_EVENT_AREA_CHANGE;
}

bool MovementEventHandler::HandleEvent(const BotEventData& eventData)
{
    if (!eventData.bot || !PlayerPatch::GetIsFaker(eventData.bot))
        return false;

    const BotMoveEventData* moveData = dynamic_cast<const BotMoveEventData*>(&eventData);
    if (!moveData)
    {
        // 如果不是移动事件数据，创建一个默认的
        BotMoveEventData defaultMoveData(eventData.eventType, eventData.bot,
            eventData.bot->GetPositionX(), eventData.bot->GetPositionY(), eventData.bot->GetPositionZ(),
            eventData.bot->GetOrientation(), eventData.bot->GetMapId(),
            eventData.bot->GetZoneId(), eventData.bot->GetAreaId());
        moveData = &defaultMoveData;
    }

    switch (eventData.eventType)
    {
        case BotEventType::BOT_EVENT_MOVE_START:
            HandleMoveStart(*moveData);
            break;
        case BotEventType::BOT_EVENT_MOVE_STOP:
            HandleMoveStop(*moveData);
            break;
        case BotEventType::BOT_EVENT_TELEPORT:
            HandleTeleport(*moveData);
            break;
        case BotEventType::BOT_EVENT_ZONE_CHANGE:
            HandleZoneChange(*moveData);
            break;
        case BotEventType::BOT_EVENT_AREA_CHANGE:
            HandleAreaChange(*moveData);
            break;
        default:
            return false;
    }
    
    return true;
}

void MovementEventHandler::HandleMoveStart(const BotMoveEventData& moveData)
{
    Player* bot = moveData.bot;
    LOG_TRACE("server", "MovementEventHandler: 机器人 {} 开始移动", bot->GetName());
    
    if (ShouldMove(bot))
    {
        MoveToRandomLocation(bot);
    }
}

void MovementEventHandler::HandleMoveStop(const BotMoveEventData& moveData)
{
    Player* bot = moveData.bot;
    LOG_TRACE("server", "MovementEventHandler: 机器人 {} 停止移动", bot->GetName());
    
    // 停止移动后，随机决定是否继续移动
    std::uniform_int_distribution<> dis(1, 100);
    if (dis(gen) <= 30) // 30% 概率继续移动
    {
        // 延迟触发下一次移动
        // 这里可以使用定时器，暂时直接触发
        TriggerRandomMovement(bot);
    }
}

void MovementEventHandler::HandleTeleport(const BotMoveEventData& moveData)
{
    Player* bot = moveData.bot;
    LOG_DEBUG("server", "MovementEventHandler: 机器人 {} 传送到 ({}, {}, {})", 
              bot->GetName(), moveData.x, moveData.y, moveData.z);
    
    // 传送后重新开始移动
    TriggerRandomMovement(bot);
}

void MovementEventHandler::HandleZoneChange(const BotMoveEventData& moveData)
{
    Player* bot = moveData.bot;
    LOG_DEBUG("server", "MovementEventHandler: 机器人 {} 进入新区域 {}", 
              bot->GetName(), moveData.zoneId);
    
    // 区域改变后适应新环境
    TriggerRandomMovement(bot);
}

void MovementEventHandler::HandleAreaChange(const BotMoveEventData& moveData)
{
    Player* bot = moveData.bot;
    LOG_TRACE("server", "MovementEventHandler: 机器人 {} 进入新地区 {}", 
              bot->GetName(), moveData.areaId);
}

bool MovementEventHandler::ShouldMove(Player* bot)
{
    // 检查是否可以移动
    if (!bot->IsAlive() ||
        bot->IsInCombat() ||
        bot->HasUnitState(UNIT_STATE_ROOT) ||
        bot->HasUnitState(UNIT_STATE_STUNNED) ||
        bot->HasUnitState(UNIT_STATE_CONFUSED) ||
        bot->HasUnitState(UNIT_STATE_FLEEING) ||
        bot->HasUnitState(UNIT_STATE_IN_FLIGHT) ||
        bot->HasUnitState(UNIT_STATE_MOVING))
    {
        return false;
    }

    // 检查机器人是否在队伍中或被设置为跟随模式
    if (bot->GetGroup() || bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
    {
        LOG_DEBUG("server", "MovementEventHandler: 机器人 {} 在队伍中或跟随模式，不应该移动", bot->GetName());
        return false;
    }

    return true;
}

void MovementEventHandler::TriggerRandomMovement(Player* bot)
{
    if (!ShouldMove(bot))
        return;
        
    // 随机延迟后移动
    std::uniform_int_distribution<> delayDis(2000, 8000);
    uint32 delay = delayDis(gen);
    
    // 这里应该使用定时器，暂时直接调用
    MoveToRandomLocation(bot);
}

void MovementEventHandler::MoveToRandomLocation(Player* bot)
{
    if (!ShouldMove(bot))
        return;
    
    // 获取当前位置
    float x = bot->GetPositionX();
    float y = bot->GetPositionY();
    float z = bot->GetPositionZ();
    
    // 随机选择方向和距离
    std::uniform_real_distribution<float> angleDis(0.0f, 2.0f * M_PI);
    std::uniform_real_distribution<float> distDis(5.0f, 20.0f);
    
    float angle = angleDis(gen);
    float dist = distDis(gen);
    
    // 计算新位置
    float newX = x + dist * cos(angle);
    float newY = y + dist * sin(angle);
    float newZ = z;
    
    // 获取地面高度
    bot->UpdateAllowedPositionZ(newX, newY, newZ);
    
    // 随机走路或跑步
    std::uniform_int_distribution<> walkDis(0, 1);
    bot->SetWalk(walkDis(gen) == 0);
    
    // 移动到新位置
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, newX, newY, newZ);
    
    LOG_TRACE("server", "MovementEventHandler: 机器人 {} 移动到 ({:.2f}, {:.2f}, {:.2f})",
              bot->GetName(), newX, newY, newZ);
}

// CombatEventHandler 实现

bool CombatEventHandler::CanHandle(BotEventType eventType) const
{
    return eventType == BotEventType::BOT_EVENT_COMBAT_START ||
           eventType == BotEventType::BOT_EVENT_COMBAT_END ||
           eventType == BotEventType::BOT_EVENT_ATTACK ||
           eventType == BotEventType::BOT_EVENT_SPELL_CAST ||
           eventType == BotEventType::BOT_EVENT_TAKE_DAMAGE ||
           eventType == BotEventType::BOT_EVENT_DEAL_DAMAGE ||
           eventType == BotEventType::BOT_EVENT_KILL ||
           eventType == BotEventType::BOT_EVENT_DEATH ||
           eventType == BotEventType::BOT_EVENT_RESURRECT;
}

bool CombatEventHandler::HandleEvent(const BotEventData& eventData)
{
    if (!eventData.bot || !PlayerPatch::GetIsFaker(eventData.bot))
        return false;

    const BotCombatEventData* combatData = dynamic_cast<const BotCombatEventData*>(&eventData);
    if (!combatData)
    {
        // 创建默认战斗事件数据
        BotCombatEventData defaultCombatData(eventData.eventType, eventData.bot);
        combatData = &defaultCombatData;
    }

    switch (eventData.eventType)
    {
        case BotEventType::BOT_EVENT_COMBAT_START:
            HandleCombatStart(*combatData);
            break;
        case BotEventType::BOT_EVENT_COMBAT_END:
            HandleCombatEnd(*combatData);
            break;
        case BotEventType::BOT_EVENT_ATTACK:
            HandleAttack(*combatData);
            break;
        case BotEventType::BOT_EVENT_SPELL_CAST:
            HandleSpellCast(*combatData);
            break;
        case BotEventType::BOT_EVENT_TAKE_DAMAGE:
            HandleTakeDamage(*combatData);
            break;
        case BotEventType::BOT_EVENT_DEAL_DAMAGE:
            HandleDealDamage(*combatData);
            break;
        case BotEventType::BOT_EVENT_KILL:
            HandleKill(*combatData);
            break;
        case BotEventType::BOT_EVENT_DEATH:
            HandleDeath(*combatData);
            break;
        case BotEventType::BOT_EVENT_RESURRECT:
            HandleResurrect(*combatData);
            break;
        default:
            return false;
    }

    return true;
}

void CombatEventHandler::HandleCombatStart(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    Unit* target = combatData.target;

    LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 进入战斗", bot->GetName());

    // ✅ 集成新的战斗AI系统
    // 简化的战斗处理
    SelectTarget(bot);
    LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 使用传统战斗逻辑", bot->GetName());
}

void CombatEventHandler::HandleCombatEnd(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 脱离战斗", bot->GetName());

    // 简化的战斗结束处理
    LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 结束战斗", bot->GetName());

    // 战斗结束后恢复正常行为
    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_MOVE_START, bot);
}

void CombatEventHandler::HandleAttack(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    Unit* target = combatData.target;

    LOG_TRACE("server", "CombatEventHandler: 机器人 {} 攻击目标", bot->GetName());

    if (target && target->IsAlive())
    {
        UseAbilities(bot, target);
    }
    else
    {
        SelectTarget(bot);
    }
}

void CombatEventHandler::HandleSpellCast(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_TRACE("server", "CombatEventHandler: 机器人 {} 施放法术 {}", bot->GetName(), combatData.spellId);
}

void CombatEventHandler::HandleTakeDamage(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_TRACE("server", "CombatEventHandler: 机器人 {} 受到 {} 点伤害", bot->GetName(), combatData.damage);

    // 受到伤害时的反应
    HandleThreat(bot);
}

void CombatEventHandler::HandleDealDamage(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_TRACE("server", "CombatEventHandler: 机器人 {} 造成 {} 点伤害", bot->GetName(), combatData.damage);
}

void CombatEventHandler::HandleKill(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 击杀目标", bot->GetName());

    // 击杀后寻找新目标
    SelectTarget(bot);
}

void CombatEventHandler::HandleDeath(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;
    LOG_INFO("server", "CombatEventHandler: 机器人 {} 死亡事件触发", bot->GetName());

    // ✅ 重构：只触发死亡事件，具体处理由BotDeathHandler统一管理
    // 避免与BotDeathHandler的功能重叠

    // 直接委托给专门的死亡处理系统
    if (sBotDeathHandler)
    {
        sBotDeathHandler->HandleBotDeath(bot);
        LOG_INFO("server", "CombatEventHandler: 机器人 {} 死亡处理已委托给BotDeathHandler", bot->GetName());
    }
    else
    {
        LOG_ERROR("server", "CombatEventHandler: BotDeathHandler未初始化，无法处理机器人 {} 的死亡", bot->GetName());
    }
}

void CombatEventHandler::HandleResurrect(const BotCombatEventData& combatData)
{
    Player* bot = combatData.bot;

    // 防止重复处理复活事件
    static std::unordered_map<uint64, uint32> lastResurrectTime;
    uint64 guid = bot->GetGUID().GetCounter();
    uint32 currentTime = getMSTime();

    // 检查是否在最近30秒内已经处理过复活事件 (大幅增加间隔)
    if (lastResurrectTime.count(guid) && (currentTime - lastResurrectTime[guid]) < 30000)
    {
        LOG_DEBUG("server", "机器人 {} 复活事件重复，忽略", bot->GetName());
        return;
    }

    lastResurrectTime[guid] = currentTime;

    // 简化的复活处理
    if (bot->IsAlive())
    {
        LOG_INFO("server", "机器人 {} 复活状态已同步", bot->GetName());
    }
    else
    {
        // 如果机器人仍在死亡状态，委托给BotDeathHandler处理
        if (sBotDeathHandler)
        {
            LOG_INFO("server", "机器人 {} 复活事件委托给BotDeathHandler处理", bot->GetName());
            // BotDeathHandler会在UpdateBot中自动处理复活逻辑
        }
        else
        {
            LOG_ERROR("server", "CombatEventHandler: BotDeathHandler未初始化，无法处理机器人 {} 的复活", bot->GetName());
        }
    }
}

void CombatEventHandler::SelectTarget(Player* bot)
{
    if (!bot->IsAlive())
        return;

    // 寻找30码范围内的敌对单位
    std::list<Creature*> creatures;
    bot->GetCreatureListWithEntryInGrid(creatures, 0, 30.0f);

    Creature* nearestTarget = nullptr;
    float nearestDist = 30.0f;

    for (auto* creature : creatures)
    {
        if (creature->IsAlive() && bot->IsValidAttackTarget(creature))
        {
            float dist = bot->GetDistance(creature);
            if (dist < nearestDist)
            {
                nearestDist = dist;
                nearestTarget = creature;
            }
        }
    }

    if (nearestTarget)
    {
        bot->Attack(nearestTarget, true);
        LOG_TRACE("server", "CombatEventHandler: 机器人 {} 选择目标 {}",
                  bot->GetName(), nearestTarget->GetName());
    }
}

void CombatEventHandler::UseAbilities(Player* bot, Unit* target)
{
    if (!bot || !target || !target->IsAlive())
        return;

    // 简单的技能使用逻辑
    // 这里可以根据职业实现更复杂的技能轮换

    // 检查是否可以使用技能
    if (bot->HasUnitState(UNIT_STATE_CASTING))
        return;

    // 随机使用一些基础技能
    std::uniform_int_distribution<> dis(1, 100);
    if (dis(gen) <= 20) // 20% 概率使用技能
    {
        // 这里可以添加职业特定的技能使用逻辑
        // 暂时只是普通攻击
        if (!bot->GetVictim())
        {
            bot->Attack(target, true);
        }
    }
}

void CombatEventHandler::HandleThreat(Player* bot)
{
    // 威胁处理逻辑
    // 可以实现逃跑、求援等行为

    if (bot->GetHealthPct() < 20.0f)
    {
        // 血量过低时尝试逃跑
        LOG_DEBUG("server", "CombatEventHandler: 机器人 {} 血量过低，尝试逃跑", bot->GetName());

        // 简单的逃跑逻辑
        float x = bot->GetPositionX();
        float y = bot->GetPositionY();
        float z = bot->GetPositionZ();

        std::uniform_real_distribution<float> angleDis(0.0f, 2.0f * M_PI);
        float angle = angleDis(gen);
        float dist = 20.0f;

        float newX = x + dist * cos(angle);
        float newY = y + dist * sin(angle);
        float newZ = z;

        bot->UpdateAllowedPositionZ(newX, newY, newZ);
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MovePoint(0, newX, newY, newZ);
    }
}

// SocialEventHandler 实现

bool SocialEventHandler::CanHandle(BotEventType eventType) const
{
    return eventType == BotEventType::BOT_EVENT_CHAT_SAY ||
           eventType == BotEventType::BOT_EVENT_CHAT_YELL ||
           eventType == BotEventType::BOT_EVENT_CHAT_WHISPER ||
           eventType == BotEventType::BOT_EVENT_CHAT_GUILD ||
           eventType == BotEventType::BOT_EVENT_CHAT_PARTY ||
           eventType == BotEventType::BOT_EVENT_CHAT_RAID ||
           eventType == BotEventType::BOT_EVENT_EMOTE;
}

bool SocialEventHandler::HandleEvent(const BotEventData& eventData)
{
    if (!eventData.bot || !PlayerPatch::GetIsFaker(eventData.bot))
        return false;

    const BotChatEventData* chatData = dynamic_cast<const BotChatEventData*>(&eventData);
    if (!chatData)
    {
        // 创建默认聊天事件数据
        BotChatEventData defaultChatData(eventData.eventType, eventData.bot, "", CHAT_MSG_SAY, LANG_COMMON);
        chatData = &defaultChatData;
    }

    switch (eventData.eventType)
    {
        case BotEventType::BOT_EVENT_CHAT_SAY:
            HandleChatSay(*chatData);
            break;
        case BotEventType::BOT_EVENT_CHAT_YELL:
            HandleChatYell(*chatData);
            break;
        case BotEventType::BOT_EVENT_CHAT_WHISPER:
            HandleChatWhisper(*chatData);
            break;
        case BotEventType::BOT_EVENT_CHAT_GUILD:
            HandleChatGuild(*chatData);
            break;
        case BotEventType::BOT_EVENT_CHAT_PARTY:
            HandleChatParty(*chatData);
            break;
        case BotEventType::BOT_EVENT_CHAT_RAID:
            HandleChatRaid(*chatData);
            break;
        case BotEventType::BOT_EVENT_EMOTE:
            HandleEmote(*chatData);
            break;
        default:
            return false;
    }

    return true;
}

void SocialEventHandler::HandleChatSay(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;

    if (chatData.sender && chatData.sender != bot)
    {
        // 响应其他玩家的聊天
        RespondToChat(bot, chatData.message, chatData.sender);
    }
    else
    {
        // 发送随机聊天
        SendRandomChat(bot);
    }
}

void SocialEventHandler::HandleChatYell(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;
    LOG_TRACE("server", "SocialEventHandler: 机器人 {} 大喊", bot->GetName());

    // 机器人通常不主动大喊，除非有特殊情况
}

void SocialEventHandler::HandleChatWhisper(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;

    if (chatData.sender && chatData.sender != bot)
    {
        LOG_DEBUG("server", "SocialEventHandler: 机器人 {} 收到来自 {} 的私聊: {}",
                  bot->GetName(), chatData.sender->GetName(), chatData.message);

        // 响应私聊
        RespondToChat(bot, chatData.message, chatData.sender);
    }
}

void SocialEventHandler::HandleChatGuild(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;

    if (bot->GetGuild())
    {
        LOG_TRACE("server", "SocialEventHandler: 机器人 {} 公会聊天", bot->GetName());

        if (chatData.sender && chatData.sender != bot)
        {
            // 响应公会聊天
            RespondToChat(bot, chatData.message, chatData.sender);
        }
    }
}

void SocialEventHandler::HandleChatParty(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;

    if (bot->GetGroup())
    {
        LOG_TRACE("server", "SocialEventHandler: 机器人 {} 队伍聊天", bot->GetName());

        if (chatData.sender && chatData.sender != bot)
        {
            // 响应队伍聊天
            RespondToChat(bot, chatData.message, chatData.sender);
        }
    }
}

void SocialEventHandler::HandleChatRaid(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;

    if (bot->GetGroup() && bot->GetGroup()->isRaidGroup())
    {
        LOG_TRACE("server", "SocialEventHandler: 机器人 {} 团队聊天", bot->GetName());

        if (chatData.sender && chatData.sender != bot)
        {
            // 响应团队聊天
            RespondToChat(bot, chatData.message, chatData.sender);
        }
    }
}

void SocialEventHandler::HandleEmote(const BotChatEventData& chatData)
{
    Player* bot = chatData.bot;
    SendRandomEmote(bot);
}

void SocialEventHandler::RespondToChat(Player* bot, const std::string& message, Player* sender)
{
    if (!bot || !sender)
        return;

    // 简单的聊天响应逻辑
    std::uniform_int_distribution<> dis(1, 100);
    if (dis(gen) <= 30) // 30% 概率响应
    {
        auto responses = GetRandomResponses();
        if (!responses.empty())
        {
            std::uniform_int_distribution<> responseDis(0, responses.size() - 1);
            std::string response = responses[responseDis(gen)];

            // 发送响应 (简化实现)
            // bot->Say(response, LANG_COMMON);

            LOG_DEBUG("server", "SocialEventHandler: 机器人 {} 响应 {}: {}",
                      bot->GetName(), sender->GetName(), response);
        }
    }
}

void SocialEventHandler::SendRandomEmote(Player* bot)
{
    auto emotes = GetRandomEmotes();
    if (!emotes.empty())
    {
        std::uniform_int_distribution<> emoteDis(0, emotes.size() - 1);
        uint32 emote = emotes[emoteDis(gen)];

        // bot->HandleEmoteCommand(emote); // 简化实现

        LOG_TRACE("server", "SocialEventHandler: 机器人 {} 发送表情 {}",
                  bot->GetName(), emote);
    }
}

void SocialEventHandler::SendRandomChat(Player* bot)
{
    auto messages = GetRandomResponses();
    if (!messages.empty())
    {
        std::uniform_int_distribution<> msgDis(0, messages.size() - 1);
        std::string message = messages[msgDis(gen)];

        // bot->Say(message, LANG_COMMON); // 简化实现

        LOG_TRACE("server", "SocialEventHandler: 机器人 {} 发送聊天: {}",
                  bot->GetName(), message);
    }
}

std::vector<std::string> SocialEventHandler::GetRandomResponses()
{
    return {
        "Hello!",
        "How are you?",
        "Nice weather today.",
        "Good luck!",
        "Have fun!",
        "See you later!",
        "Thanks!",
        "You're welcome!",
        "Interesting...",
        "I agree.",
        "That's cool!",
        "Really?",
        "Awesome!",
        "Good job!",
        "Well done!"
    };
}

std::vector<uint32> SocialEventHandler::GetRandomEmotes()
{
    return {
        1, // EMOTE_ONESHOT_WAVE
        2, // EMOTE_ONESHOT_BOW
        3, // EMOTE_ONESHOT_CHEER
        4, // EMOTE_ONESHOT_DANCE
        5, // EMOTE_ONESHOT_LAUGH
        6, // EMOTE_ONESHOT_APPLAUD
        7, // EMOTE_ONESHOT_POINT
        8, // EMOTE_ONESHOT_SALUTE
        9, // EMOTE_ONESHOT_NOD
        10 // EMOTE_ONESHOT_FLEX
    };
}

// InteractionResponseEventHandler 实现 (新增)

bool InteractionResponseEventHandler::CanHandle(BotEventType eventType) const
{
    return eventType == BotEventType::BOT_EVENT_GUILD_INVITE ||
           eventType == BotEventType::BOT_EVENT_GROUP_INVITE ||
           eventType == BotEventType::BOT_EVENT_ARENA_TEAM_INVITE ||
           eventType == BotEventType::BOT_EVENT_TRADE_REQUEST ||
           eventType == BotEventType::BOT_EVENT_TRADE_BEGIN ||
           eventType == BotEventType::BOT_EVENT_TRADE_ACCEPT ||
           eventType == BotEventType::BOT_EVENT_DUEL_REQUEST ||
           eventType == BotEventType::BOT_EVENT_GUILD_CHARTER ||
           eventType == BotEventType::BOT_EVENT_ARENA_CHARTER ||
           eventType == BotEventType::BOT_EVENT_BATTLEFIELD_INVITE;
}

bool InteractionResponseEventHandler::HandleEvent(const BotEventData& eventData)
{
    if (!eventData.bot || !PlayerPatch::GetIsFaker(eventData.bot))
        return false;

    // 初始化响应概率（如果还未初始化）
    static bool initialized = false;
    if (!initialized)
    {
        InitializeResponseProbabilities();
        initialized = true;
    }

    switch (eventData.eventType)
    {
        case BotEventType::BOT_EVENT_GUILD_INVITE:
        {
            const BotInteractionEventData& interactionData = static_cast<const BotInteractionEventData&>(eventData);
            HandleGuildInvite(interactionData);
            break;
        }
        case BotEventType::BOT_EVENT_GROUP_INVITE:
        {
            const BotInteractionEventData& interactionData = static_cast<const BotInteractionEventData&>(eventData);
            HandleGroupInvite(interactionData);
            break;
        }
        case BotEventType::BOT_EVENT_ARENA_TEAM_INVITE:
        {
            const BotInteractionEventData& interactionData = static_cast<const BotInteractionEventData&>(eventData);
            HandleArenaTeamInvite(interactionData);
            break;
        }
        case BotEventType::BOT_EVENT_TRADE_REQUEST:
        {
            const BotTradeEventData& tradeData = static_cast<const BotTradeEventData&>(eventData);
            HandleTradeRequest(tradeData);
            break;
        }
        case BotEventType::BOT_EVENT_TRADE_BEGIN:
        {
            const BotTradeEventData& tradeData = static_cast<const BotTradeEventData&>(eventData);
            HandleTradeBegin(tradeData);
            break;
        }
        case BotEventType::BOT_EVENT_TRADE_ACCEPT:
        {
            const BotTradeEventData& tradeData = static_cast<const BotTradeEventData&>(eventData);
            HandleTradeAccept(tradeData);
            break;
        }
        case BotEventType::BOT_EVENT_DUEL_REQUEST:
        {
            const BotInteractionEventData& interactionData = static_cast<const BotInteractionEventData&>(eventData);
            HandleDuelRequest(interactionData);
            break;
        }
        case BotEventType::BOT_EVENT_GUILD_CHARTER:
        {
            const BotPetitionEventData& petitionData = static_cast<const BotPetitionEventData&>(eventData);
            HandleGuildCharter(petitionData);
            break;
        }
        case BotEventType::BOT_EVENT_ARENA_CHARTER:
        {
            const BotPetitionEventData& petitionData = static_cast<const BotPetitionEventData&>(eventData);
            HandleArenaCharter(petitionData);
            break;
        }
        case BotEventType::BOT_EVENT_BATTLEFIELD_INVITE:
        {
            const BotInteractionEventData& interactionData = static_cast<const BotInteractionEventData&>(eventData);
            HandleBattlefieldInvite(interactionData);
            break;
        }
        default:
            return false;
    }

    return true;
}

// BotEventHandlerFactory 实现

void BotEventHandlerFactory::RegisterAllHandlers()
{
    LOG_INFO("server", "BotEventHandlerFactory: 注册所有事件处理器");

    // 清理现有处理器
    UnregisterAllHandlers();

    // 创建并注册基础事件处理器
    auto basicHandler = std::make_shared<BasicBotEventHandler>();
    handlers.push_back(basicHandler);
    sBotEventMgr->RegisterHandler(basicHandler, {
        BotEventType::BOT_EVENT_LOGIN,
        BotEventType::BOT_EVENT_LOGOUT,
        BotEventType::BOT_EVENT_UPDATE,
        BotEventType::BOT_EVENT_LEVEL_UP
    });

    // 创建并注册移动事件处理器
    auto movementHandler = std::make_shared<MovementEventHandler>();
    handlers.push_back(movementHandler);
    sBotEventMgr->RegisterHandler(movementHandler, {
        BotEventType::BOT_EVENT_MOVE_START,
        BotEventType::BOT_EVENT_MOVE_STOP,
        BotEventType::BOT_EVENT_TELEPORT,
        BotEventType::BOT_EVENT_ZONE_CHANGE,
        BotEventType::BOT_EVENT_AREA_CHANGE
    });

    // 创建并注册战斗事件处理器
    auto combatHandler = std::make_shared<CombatEventHandler>();
    handlers.push_back(combatHandler);
    sBotEventMgr->RegisterHandler(combatHandler, {
        BotEventType::BOT_EVENT_COMBAT_START,
        BotEventType::BOT_EVENT_COMBAT_END,
        BotEventType::BOT_EVENT_ATTACK,
        BotEventType::BOT_EVENT_SPELL_CAST,
        BotEventType::BOT_EVENT_TAKE_DAMAGE,
        BotEventType::BOT_EVENT_DEAL_DAMAGE,
        BotEventType::BOT_EVENT_KILL,
        BotEventType::BOT_EVENT_DEATH,
        BotEventType::BOT_EVENT_RESURRECT
    });

    // 创建并注册社交事件处理器
    auto socialHandler = std::make_shared<SocialEventHandler>();
    handlers.push_back(socialHandler);
    sBotEventMgr->RegisterHandler(socialHandler, {
        BotEventType::BOT_EVENT_CHAT_SAY,
        BotEventType::BOT_EVENT_CHAT_YELL,
        BotEventType::BOT_EVENT_CHAT_WHISPER,
        BotEventType::BOT_EVENT_CHAT_GUILD,
        BotEventType::BOT_EVENT_CHAT_PARTY,
        BotEventType::BOT_EVENT_CHAT_RAID,
        BotEventType::BOT_EVENT_EMOTE
    });

    // 创建并注册交互响应事件处理器 (新增)
    auto interactionHandler = std::make_shared<InteractionResponseEventHandler>();
    handlers.push_back(interactionHandler);
    sBotEventMgr->RegisterHandler(interactionHandler, {
        BotEventType::BOT_EVENT_GUILD_INVITE,
        BotEventType::BOT_EVENT_GROUP_INVITE,
        BotEventType::BOT_EVENT_ARENA_TEAM_INVITE,
        BotEventType::BOT_EVENT_TRADE_REQUEST,
        BotEventType::BOT_EVENT_TRADE_BEGIN,
        BotEventType::BOT_EVENT_TRADE_ACCEPT,
        BotEventType::BOT_EVENT_DUEL_REQUEST,
        BotEventType::BOT_EVENT_GUILD_CHARTER,
        BotEventType::BOT_EVENT_ARENA_CHARTER,
        BotEventType::BOT_EVENT_BATTLEFIELD_INVITE
    });

    LOG_INFO("server", "BotEventHandlerFactory: 注册了 {} 个事件处理器", handlers.size());
}

void BotEventHandlerFactory::UnregisterAllHandlers()
{
    LOG_INFO("server", "BotEventHandlerFactory: 注销所有事件处理器");

    for (auto& handler : handlers)
    {
        sBotEventMgr->UnregisterHandler(handler);
    }

    handlers.clear();
}

// InteractionResponseEventHandler 实现

// 初始化响应概率
void InteractionResponseEventHandler::InitializeResponseProbabilities()
{
    LOG_INFO("server", "InteractionResponseEventHandler: 初始化响应概率");

    // 设置默认响应概率
    for (uint32 i = 0; i < RESPONSE_MAX; ++i)
    {
        _responseProbability[i] = 0.7f; // 默认70%概率接受邀请
    }

    // 从配置文件读取响应概率
    _responseProbability[RESPONSE_GUILD_INVITE] = sConfigMgr->GetOption<int32>("YGbot.Response.GuildInvite", 70) / 100.0f;
    _responseProbability[RESPONSE_GROUP_INVITE] = sConfigMgr->GetOption<int32>("YGbot.Response.GroupInvite", 80) / 100.0f;
    _responseProbability[RESPONSE_ARENA_TEAM_INVITE] = sConfigMgr->GetOption<int32>("YGbot.Response.ArenaTeamInvite", 60) / 100.0f;
    _responseProbability[RESPONSE_TRADE_REQUEST] = sConfigMgr->GetOption<int32>("YGbot.Response.TradeRequest", 75) / 100.0f;
    _responseProbability[RESPONSE_TRADE_ACCEPT] = sConfigMgr->GetOption<int32>("YGbot.Response.TradeAccept", 90) / 100.0f;
    _responseProbability[RESPONSE_GUILD_CHARTER] = sConfigMgr->GetOption<int32>("YGbot.Response.GuildCharter", 60) / 100.0f;
    _responseProbability[RESPONSE_ARENA_CHARTER] = sConfigMgr->GetOption<int32>("YGbot.Response.ArenaCharter", 50) / 100.0f;
    _responseProbability[RESPONSE_DUEL_REQUEST] = sConfigMgr->GetOption<int32>("YGbot.Response.DuelRequest", 80) / 100.0f;
    _responseProbability[RESPONSE_BATTLEFIELD_INVITE] = sConfigMgr->GetOption<int32>("YGbot.Response.BattlefieldInvite", 70) / 100.0f;

    LOG_INFO("server", "InteractionResponseEventHandler: 响应概率初始化完成");
}

// 决策逻辑
bool InteractionResponseEventHandler::ShouldRespond(ResponseType responseType, Player* bot)
{
    if (responseType >= RESPONSE_MAX)
    {
        LOG_ERROR("server", "InteractionResponseEventHandler: 无效的响应类型: {}", responseType);
        return false;
    }

    float probability = _responseProbability[responseType];
    float randomValue = static_cast<float>(rand()) / RAND_MAX;

    bool shouldRespond = randomValue < probability;

    if (bot)
    {
        LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 响应决策 - 类型: {}, 概率: {:.2f}, 随机值: {:.2f}, 结果: {}",
            bot->GetName(), responseType, probability, randomValue, shouldRespond ? "接受" : "拒绝");
    }

    return shouldRespond;
}

float InteractionResponseEventHandler::GetResponseProbability(ResponseType responseType)
{
    if (responseType >= RESPONSE_MAX)
        return 0.0f;
    return _responseProbability[responseType];
}

void InteractionResponseEventHandler::SetResponseProbability(ResponseType responseType, float probability)
{
    if (responseType >= RESPONSE_MAX)
        return;
    _responseProbability[responseType] = std::max(0.0f, std::min(1.0f, probability));
}

// 延迟响应
void InteractionResponseEventHandler::DelayedResponse(Player* bot, ResponseType responseType, std::function<void()> responseAction, uint32 delay)
{
    if (!bot || !responseAction)
    {
        LOG_ERROR("server", "InteractionResponseEventHandler: DelayedResponse 参数无效");
        return;
    }

    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 将在 {} 毫秒后执行响应 (类型: {})", bot->GetName(), delay, responseType);

    g_interactionScheduler.Schedule(std::chrono::milliseconds(delay), [bot, responseAction, responseType](TaskContext /*context*/) {
        if (bot && bot->IsInWorld())
        {
            try
            {
                LOG_INFO("server", "InteractionResponseEventHandler: 执行延迟响应 (类型: {})", responseType);
                responseAction();
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("server", "InteractionResponseEventHandler: 延迟响应执行异常: {}", e.what());
            }
        }
        else
        {
            LOG_WARN("server", "InteractionResponseEventHandler: 机器人已离线，取消响应");
        }
    });
}

// 处理公会邀请
void InteractionResponseEventHandler::HandleGuildInvite(const BotInteractionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;

    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到公会邀请", bot->GetName());

    // 决定是否接受邀请
    bool shouldAccept = ShouldRespond(RESPONSE_GUILD_INVITE, bot);
    uint32 delay = urand(1000, 5000);

    if (shouldAccept)
    {
        DelayedResponse(bot, RESPONSE_GUILD_INVITE, [bot]() {
            WorldSession* session = bot->GetSession();
            if (session)
            {
                // 直接调用公会接受处理函数
                if (Guild* guild = sGuildMgr->GetGuildById(bot->GetGuildIdInvited()))
                {
                    guild->HandleAcceptMember(session);
                    LOG_INFO("server", "机器人 {} 接受了公会邀请", bot->GetName());
                }
            }
        }, delay);
    }
    else
    {
        DelayedResponse(bot, RESPONSE_GUILD_INVITE, [bot]() {
            // 直接设置公会邀请ID为0来拒绝邀请
            bot->SetGuildIdInvited(0);
            bot->SetInGuild(0);
            LOG_INFO("server", "机器人 {} 拒绝了公会邀请", bot->GetName());
        }, delay);
    }
}

// 处理组队邀请
void InteractionResponseEventHandler::HandleGroupInvite(const BotInteractionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;

    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到组队邀请", bot->GetName());

    // 决定是否接受邀请
    bool shouldAccept = ShouldRespond(RESPONSE_GROUP_INVITE, bot);
    uint32 delay = urand(1000, 3000);

    if (shouldAccept)
    {
        DelayedResponse(bot, RESPONSE_GROUP_INVITE, [bot]() {
            WorldSession* session = bot->GetSession();
            Group* group = bot->GetGroupInvite();
            if (session && group)
            {
                // 直接调用组队接受处理函数
                WorldPacket acceptPacket(CMSG_GROUP_ACCEPT, 4);
                acceptPacket << uint32(0); // roles_mask
                session->HandleGroupAcceptOpcode(acceptPacket);
                LOG_INFO("server", "机器人 {} 接受了组队邀请", bot->GetName());
            }
            else
            {
                LOG_WARN("server", "机器人 {} 没有有效的组队邀请", bot->GetName());
            }
        }, delay);
    }
    else
    {
        DelayedResponse(bot, RESPONSE_GROUP_INVITE, [bot]() {
            WorldSession* session = bot->GetSession();
            Group* group = bot->GetGroupInvite();
            if (session && group)
            {
                // 直接调用组队拒绝处理函数
                WorldPacket declinePacket(CMSG_GROUP_DECLINE, 0);
                session->HandleGroupDeclineOpcode(declinePacket);
                LOG_INFO("server", "机器人 {} 拒绝了组队邀请", bot->GetName());
            }
            else
            {
                LOG_WARN("server", "机器人 {} 没有有效的组队邀请可拒绝", bot->GetName());
            }
        }, delay);
    }
}

// 处理竞技场战队邀请
void InteractionResponseEventHandler::HandleArenaTeamInvite(const BotInteractionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;

    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到竞技场战队邀请", bot->GetName());

    // 决定是否接受邀请
    bool shouldAccept = ShouldRespond(RESPONSE_ARENA_TEAM_INVITE, bot);
    uint32 delay = urand(1000, 5000);

    if (shouldAccept)
    {
        DelayedResponse(bot, RESPONSE_ARENA_TEAM_INVITE, [bot]() {
            // 直接调用竞技场战队接受处理函数
            ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(bot->GetArenaTeamIdInvited());
            if (arenaTeam && !bot->GetArenaTeamId(arenaTeam->GetSlot()))
            {
                arenaTeam->AddMember(bot->GetGUID());
                bot->SetArenaTeamIdInvited(0);
                LOG_INFO("server", "机器人 {} 接受了竞技场战队邀请", bot->GetName());
            }
        }, delay);
    }
    else
    {
        DelayedResponse(bot, RESPONSE_ARENA_TEAM_INVITE, [bot]() {
            // 直接设置竞技场战队邀请ID为0来拒绝邀请
            bot->SetArenaTeamIdInvited(0);
            LOG_INFO("server", "机器人 {} 拒绝了竞技场战队邀请", bot->GetName());
        }, delay);
    }
}

// 空的脚本注册函数 - 这个文件包含事件处理器实现，不是脚本
void AddSC_BotEventHandlers()
{
    // 这个文件只包含事件处理器实现，不需要注册脚本
}
