#include "BotMovementManager.h"
#include "BotTerrainValidator.h"
#include "BotMovementTasks.h"
#include "Log.h"
#include "Player.h"
#include <chrono>

// ========================================
// 移动系统测试函数
// ========================================

// 测试地形验证器
bool TestTerrainValidator()
{
    LOG_INFO("movement", "开始测试地形验证器...");
    
    // 这里可以添加具体的测试逻辑
    // 由于需要Player对象，暂时返回true
    
    LOG_INFO("movement", "地形验证器测试完成");
    return true;
}

// 测试移动管理器
bool TestMovementManager()
{
    LOG_INFO("movement", "开始测试移动管理器...");
    
    // 初始化移动管理器
    sBotMovementManager.Initialize();
    
    // 获取统计信息
    auto stats = sBotMovementManager.GetStats();
    LOG_INFO("movement", "移动管理器统计: 总任务数={}, 完成任务数={}, 失败任务数={}", 
             stats.totalTasks, stats.completedTasks, stats.failedTasks);
    
    LOG_INFO("movement", "移动管理器测试完成");
    return true;
}

// 测试移动任务工厂
bool TestMovementTaskFactory()
{
    LOG_INFO("movement", "开始测试移动任务工厂...");
    
    // 测试创建移动任务
    auto moveTask = BotMovementTaskFactory::CreateMoveToPosition(0, 0, 0);
    if (!moveTask)
    {
        LOG_ERROR("movement", "创建移动任务失败");
        return false;
    }
    
    // 测试创建随机漫步任务
    auto randomWalkTask = BotMovementTaskFactory::CreateRandomWalkTask(20.0f);
    if (!randomWalkTask)
    {
        LOG_ERROR("movement", "创建随机漫步任务失败");
        return false;
    }
    
    LOG_INFO("movement", "移动任务工厂测试完成");
    return true;
}

// 运行所有测试
bool RunMovementSystemTests()
{
    LOG_INFO("movement", "========================================");
    LOG_INFO("movement", "开始运行移动系统测试...");
    LOG_INFO("movement", "========================================");
    
    bool allTestsPassed = true;
    
    // 测试地形验证器
    if (!TestTerrainValidator())
    {
        LOG_ERROR("movement", "地形验证器测试失败");
        allTestsPassed = false;
    }
    
    // 测试移动管理器
    if (!TestMovementManager())
    {
        LOG_ERROR("movement", "移动管理器测试失败");
        allTestsPassed = false;
    }
    
    // 测试移动任务工厂
    if (!TestMovementTaskFactory())
    {
        LOG_ERROR("movement", "移动任务工厂测试失败");
        allTestsPassed = false;
    }
    
    LOG_INFO("movement", "========================================");
    if (allTestsPassed)
    {
        LOG_INFO("movement", "所有移动系统测试通过！");
    }
    else
    {
        LOG_ERROR("movement", "部分移动系统测试失败！");
    }
    LOG_INFO("movement", "========================================");
    
    return allTestsPassed;
}

// ========================================
// 移动系统示例用法
// ========================================

// 示例：如何使用移动系统
void MovementSystemUsageExample(Player* bot)
{
    if (!bot)
        return;
    
    LOG_INFO("movement", "移动系统使用示例 - 机器人: {}", bot->GetName());
    
    // 1. 移动到指定位置
    float targetX = bot->GetPositionX() + 10.0f;
    float targetY = bot->GetPositionY() + 10.0f;
    float targetZ = bot->GetPositionZ();
    
    uint32 moveTaskId = sBotMovementManager.MoveToPosition(bot, targetX, targetY, targetZ);
    if (moveTaskId > 0)
    {
        LOG_INFO("movement", "创建移动任务成功，任务ID: {}", moveTaskId);
    }
    
    // 2. 开始随机漫步
    uint32 randomWalkTaskId = sBotMovementManager.StartRandomWalk(bot, 15.0f);
    if (randomWalkTaskId > 0)
    {
        LOG_INFO("movement", "创建随机漫步任务成功，任务ID: {}", randomWalkTaskId);
    }
    
    // 3. 检查移动状态
    bool isMoving = sBotMovementManager.IsMoving(bot);
    LOG_INFO("movement", "机器人移动状态: {}", isMoving ? "移动中" : "静止");
    
    // 4. 获取当前任务
    auto currentTask = sBotMovementManager.GetCurrentTask(bot);
    if (currentTask)
    {
        LOG_INFO("movement", "当前任务ID: {}, 类型: {}", 
                 currentTask->GetTaskId(), static_cast<int>(currentTask->GetType()));
    }
}

// 示例：如何验证位置安全性
void PositionValidationExample(Player* bot, float x, float y, float z)
{
    if (!bot)
        return;
    
    LOG_INFO("movement", "位置验证示例 - 位置: ({:.2f}, {:.2f}, {:.2f})", x, y, z);
    
    // 验证位置
    auto validation = sBotTerrainValidator.ValidatePosition(bot, x, y, z);
    
    if (validation.isValid)
    {
        LOG_INFO("movement", "位置验证通过 - 地形类型: {}", static_cast<int>(validation.terrainType));
        LOG_INFO("movement", "地面高度: {:.2f}, 可行走: {}, 可游泳: {}, 可飞行: {}", 
                 validation.groundHeight, validation.canWalk, validation.canSwim, validation.canFly);
    }
    else
    {
        LOG_WARN("movement", "位置验证失败 - 错误: {}", validation.errorMessage);
        
        // 尝试修正位置
        float correctedX = x, correctedY = y, correctedZ = z;
        if (sBotTerrainValidator.CorrectPosition(bot, correctedX, correctedY, correctedZ))
        {
            LOG_INFO("movement", "位置修正成功 - 新位置: ({:.2f}, {:.2f}, {:.2f})", 
                     correctedX, correctedY, correctedZ);
        }
        else
        {
            LOG_ERROR("movement", "位置修正失败");
        }
    }
}

// 示例：如何生成安全路径
void PathGenerationExample(Player* bot, float startX, float startY, float startZ, 
                          float endX, float endY, float endZ)
{
    if (!bot)
        return;
    
    LOG_INFO("movement", "路径生成示例 - 起点: ({:.2f}, {:.2f}, {:.2f}), 终点: ({:.2f}, {:.2f}, {:.2f})", 
             startX, startY, startZ, endX, endY, endZ);
    
    // 验证路径
    auto pathValidation = sBotTerrainValidator.ValidatePath(bot, startX, startY, startZ, 
                                                           endX, endY, endZ);
    
    if (pathValidation.isValid)
    {
        LOG_INFO("movement", "路径验证通过 - 总距离: {:.2f}, 路径点数: {}", 
                 pathValidation.totalDistance, pathValidation.validPath.size());
        
        // 显示路径点
        for (size_t i = 0; i < pathValidation.validPath.size() && i < 5; ++i)
        {
            const auto& point = pathValidation.validPath[i];
            LOG_INFO("movement", "路径点 {}: ({:.2f}, {:.2f}, {:.2f})", 
                     i, point.x, point.y, point.z);
        }
    }
    else
    {
        LOG_WARN("movement", "路径验证失败 - 错误: {}", pathValidation.errorMessage);
        LOG_WARN("movement", "失败原因: {}", static_cast<int>(pathValidation.failureReason));
    }
}

// ========================================
// 性能测试
// ========================================

// 性能测试：位置验证
void PerformanceTestPositionValidation(Player* bot, int iterations = 1000)
{
    if (!bot)
        return;
    
    LOG_INFO("movement", "开始位置验证性能测试，迭代次数: {}", iterations);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i)
    {
        float x = bot->GetPositionX() + (i % 20 - 10);
        float y = bot->GetPositionY() + (i % 20 - 10);
        float z = bot->GetPositionZ();
        
        sBotTerrainValidator.ValidatePosition(bot, x, y, z);
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    LOG_INFO("movement", "位置验证性能测试完成 - 耗时: {} 毫秒, 平均每次: {:.2f} 毫秒", 
             duration.count(), static_cast<float>(duration.count()) / iterations);
}

// 性能测试：路径生成
void PerformanceTestPathGeneration(Player* bot, int iterations = 100)
{
    if (!bot)
        return;
    
    LOG_INFO("movement", "开始路径生成性能测试，迭代次数: {}", iterations);
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i)
    {
        float startX = bot->GetPositionX();
        float startY = bot->GetPositionY();
        float startZ = bot->GetPositionZ();
        
        float endX = startX + (i % 40 - 20);
        float endY = startY + (i % 40 - 20);
        float endZ = startZ;
        
        sBotTerrainValidator.ValidatePath(bot, startX, startY, startZ, endX, endY, endZ);
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    LOG_INFO("movement", "路径生成性能测试完成 - 耗时: {} 毫秒, 平均每次: {:.2f} 毫秒", 
             duration.count(), static_cast<float>(duration.count()) / iterations);
}
