#include "CombatAI.h"
#include "CombatSystemCore.h"
#include "Player.h"
#include "SpellMgr.h"
#include "SpellInfo.h"
#include "Log.h"

CombatAI::CombatAI(Player* player)
    : _player(player)
    , _combatState(COMBAT_STATE_IDLE)
    , _scenarioType(SCENARIO_FIELD)
    , _currentTarget(nullptr)
    , _targetUpdateTimer(0)
    , _movementUpdateTimer(0)
    , _spellUpdateTimer(0)
    , _combatTimer(0)
    , _combatRange(COMBAT_MELEE_RANGE)
    , _fleeRange(COMBAT_SEARCH_RANGE)
    , _reactionTime(500)
{
    if (_player)
    {
        _scenarioType = sCombatSystem->DetectCombatScenario(_player);
        LOG_DEBUG("combat", "创建战斗AI for 玩家 {} (场景: {})",
                 _player->GetName(), static_cast<uint32>(_scenarioType));

        // 初始化法术
        InitializeSpells();
    }
}

void CombatAI::Update(uint32 diff)
{
    if (!_player || !_player->IsInWorld())
        return;
    
    // 更新计时器
    _targetUpdateTimer += diff;
    _movementUpdateTimer += diff;
    _spellUpdateTimer += diff;
    _combatTimer += diff;
    
    // 更新法术冷却
    UpdateSpellCooldowns(diff);
    
    // 更新战斗状态
    UpdateCombatState();
    
    // 每500ms更新目标列表
    if (_targetUpdateTimer >= 500)
    {
        UpdateTargetList();
        _targetUpdateTimer = 0;
    }
    
    // 每100ms更新移动，确保及时跟随目标
    if (_movementUpdateTimer >= 100)
    {
        UpdateMovement();
        _movementUpdateTimer = 0;
    }
    
    // 每100ms处理战斗逻辑
    if (_spellUpdateTimer >= 100)
    {
        ProcessCombatLogic();
        _spellUpdateTimer = 0;
    }
}

void CombatAI::EnterCombat(Unit* target)
{
    if (!target)
        return;

    _currentTarget = target;
    _combatState = COMBAT_STATE_ENGAGING;
    _combatTimer = 0;

    // 立即开始移动到目标
    MoveToTarget(target);

    LogCombatAction("进入战斗", target);

    // 记录战斗开始
    sCombatSystem->RecordCombatAction(_player, "combat_start");
}

void CombatAI::LeaveCombat()
{
    _currentTarget = nullptr;
    _combatState = COMBAT_STATE_IDLE;
    
    // 清理目标列表
    ClearTargets();
    
    LogCombatAction("离开战斗");
    
    // 记录战斗结束
    sCombatSystem->RecordCombatAction(_player, "combat_end");
}

void CombatAI::OnDamageReceived(Unit* attacker, uint32 damage)
{
    if (!attacker)
        return;
    
    // 记录伤害
    sCombatSystem->RecordCombatAction(_player, "damage_taken", damage);
    
    // 如果不在战斗中，进入战斗
    if (_combatState == COMBAT_STATE_IDLE)
    {
        EnterCombat(attacker);
    }
    
    // 检查是否需要逃跑
    if (ShouldFlee())
    {
        _combatState = COMBAT_STATE_FLEEING;
    }
}

void CombatAI::OnDamageDealt(Unit* target, uint32 damage)
{
    if (!target)
        return;
    
    // 记录伤害
    sCombatSystem->RecordCombatAction(_player, "damage_dealt", damage);
}

void CombatAI::OnSpellCast(uint32 spellId, Unit* target)
{
    // 记录法术施放
    sCombatSystem->RecordCombatAction(_player, "spell_cast");
    
    // 设置法术冷却
    const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
    if (spellInfo)
    {
        uint32 cooldown = spellInfo->RecoveryTime;
        if (cooldown > 0)
        {
            SetSpellCooldown(spellId, cooldown);
        }
    }
    
    LogSpellCast(spellId, target);
}

Unit* CombatAI::SelectTarget()
{
    if (_targetList.empty())
        return nullptr;
    
    Unit* bestTarget = nullptr;
    float bestPriority = 0.0f;
    
    for (const auto& targetInfo : _targetList)
    {
        if (!targetInfo.unit || !targetInfo.unit->IsAlive())
            continue;
        
        float priority = CalculateTargetPriority(targetInfo.unit);
        if (priority > bestPriority)
        {
            bestPriority = priority;
            bestTarget = targetInfo.unit;
        }
    }
    
    return bestTarget;
}

void CombatAI::UpdateTargetList()
{
    if (!_player)
        return;
    
    // 清理无效目标
    _targetList.erase(
        std::remove_if(_targetList.begin(), _targetList.end(),
            [](const CombatTarget& target) {
                return !target.unit || !target.unit->IsAlive();
            }),
        _targetList.end()
    );
    
    // 搜索新目标
    std::vector<Unit*> nearbyEnemies = sCombatSystem->FindNearbyEnemies(_player, COMBAT_SEARCH_RANGE);
    
    for (Unit* enemy : nearbyEnemies)
    {
        // 检查是否已在目标列表中
        bool found = false;
        for (const auto& target : _targetList)
        {
            if (target.unit == enemy)
            {
                found = true;
                break;
            }
        }
        
        if (!found)
        {
            AddTarget(enemy);
        }
    }
}

bool CombatAI::IsValidTarget(Unit* target)
{
    if (!target || !_player)
        return false;
    
    return sCombatSystem->IsValidCombatTarget(_player, target);
}

float CombatAI::CalculateTargetPriority(Unit* target)
{
    if (!target || !_player)
        return 0.0f;
    
    return sCombatSystem->CalculateThreatLevel(_player, target);
}

bool CombatAI::CastSpell(uint32 spellId, Unit* target)
{
    if (!_player || !CanCastSpell(spellId, target))
        return false;
    
    // 使用CastSpell进行施法
    SpellCastTargets targets;
    if (target)
    {
        targets.SetUnitTarget(target);
    }
    
    Spell* spell = new Spell(_player, sSpellMgr->GetSpellInfo(spellId), TRIGGERED_NONE);
    spell->prepare(&targets);
    
    // 触发回调
    OnSpellCast(spellId, target);
    
    return true;
}

bool CombatAI::CanCastSpell(uint32 spellId, Unit* target)
{
    if (!_player)
        return false;
    
    // 检查是否有法术
    if (!HasSpell(spellId))
        return false;
    
    // 检查冷却
    if (!IsSpellReady(spellId))
        return false;
    
    // 检查法力
    if (!HasEnoughMana(spellId))
        return false;
    
    // 检查距离
    if (target && !IsInSpellRange(target, spellId))
        return false;
    
    return true;
}

void CombatAI::UpdateSpellCooldowns(uint32 diff)
{
    for (auto it = _spellCooldowns.begin(); it != _spellCooldowns.end();)
    {
        if (it->second <= diff)
        {
            it = _spellCooldowns.erase(it);
        }
        else
        {
            it->second -= diff;
            ++it;
        }
    }
}

void CombatAI::UpdateMovement()
{
    if (!_player)
        return;

    switch (_combatState)
    {
        case COMBAT_STATE_ENGAGING:
        case COMBAT_STATE_FIGHTING:
            if (_currentTarget && _currentTarget->IsAlive())
            {
                // 持续更新移动，确保跟随目标
                MoveToTarget(_currentTarget);
            }
            else
            {
                // 当前目标无效，寻找新目标
                _currentTarget = SelectTarget();
                if (_currentTarget)
                {
                    MoveToTarget(_currentTarget);
                }
            }
            break;

        case COMBAT_STATE_FLEEING:
            Flee();
            break;

        case COMBAT_STATE_IDLE:
            // 空闲状态，停止移动
            if (_player->GetMotionMaster()->GetCurrentMovementGeneratorType() == CHASE_MOTION_TYPE ||
                _player->GetMotionMaster()->GetCurrentMovementGeneratorType() == POINT_MOTION_TYPE)
            {
                _player->GetMotionMaster()->Clear();
                _player->GetMotionMaster()->MoveIdle();
            }
            break;

        default:
            break;
    }
}

void CombatAI::MoveToTarget(Unit* target)
{
    if (!target || !_player)
        return;

    float distance = _player->GetDistance(target);
    float optimalRange = GetOptimalRange(target);
    float tolerance = COMBAT_MOVEMENT_TOLERANCE;

    // 确保机器人面向目标
    if (!_player->HasInArc(M_PI / 4, target))
    {
        _player->SetFacingToObject(target);
    }

    // 检查是否需要移动
    if (distance > optimalRange + tolerance)
    {
        // 太远，需要靠近 - 使用MoveChase确保持续跟随
        _player->GetMotionMaster()->Clear();
        _player->GetMotionMaster()->MoveChase(target, optimalRange);
        LogMovement("追击目标");
    }
    else if (distance < optimalRange - tolerance && optimalRange > 1.0f)
    {
        // 太近，需要后退（只有远程职业才后退）
        float angle = target->GetAngle(_player) + M_PI;
        float backDistance = optimalRange + 1.0f;
        float x = _player->GetPositionX() + cos(angle) * backDistance;
        float y = _player->GetPositionY() + sin(angle) * backDistance;
        float z = _player->GetPositionZ();

        _player->GetMotionMaster()->Clear();
        _player->GetMotionMaster()->MovePoint(0, x, y, z);
        LogMovement("后退保持距离");
    }
    else
    {
        // 在合适范围内，确保停止移动并面向目标
        if (_player->GetMotionMaster()->GetCurrentMovementGeneratorType() == CHASE_MOTION_TYPE ||
            _player->GetMotionMaster()->GetCurrentMovementGeneratorType() == POINT_MOTION_TYPE)
        {
            _player->GetMotionMaster()->Clear();
            _player->GetMotionMaster()->MoveIdle();
        }
        _player->SetFacingToObject(target);
    }
}

bool CombatAI::ShouldFlee()
{
    if (!_player)
        return false;
    
    // 血量过低
    if (GetHealthPercentage() < COMBAT_FLEE_HEALTH_PCT)
        return true;
    
    // 面对过多敌人
    if (_targetList.size() > 3)
        return true;
    
    return false;
}

bool CombatAI::ShouldHeal()
{
    if (!_player)
        return false;
    
    return GetHealthPercentage() < 50;
}

bool CombatAI::ShouldBuff()
{
    if (!_player)
        return false;
    
    // 不在战斗中时可以加BUFF
    return _combatState == COMBAT_STATE_IDLE;
}

bool CombatAI::NeedsMana()
{
    if (!_player)
        return false;
    
    return GetManaPercentage() < COMBAT_MANA_RESERVE_PCT;
}

void CombatAI::UpdateCombatState()
{
    if (!_player)
        return;
    
    // 根据当前情况更新战斗状态
    if (_player->IsInCombat())
    {
        if (_combatState == COMBAT_STATE_IDLE)
        {
            _combatState = COMBAT_STATE_ENGAGING;
        }
        else if (_combatState == COMBAT_STATE_ENGAGING)
        {
            _combatState = COMBAT_STATE_FIGHTING;
        }
    }
    else
    {
        if (_combatState != COMBAT_STATE_IDLE)
        {
            LeaveCombat();
        }
    }
}

void CombatAI::ProcessCombatLogic()
{
    if (!_player)
        return;
    
    switch (_combatState)
    {
        case COMBAT_STATE_IDLE:
            // 空闲状态，检查是否需要BUFF
            if (ShouldBuff())
            {
                uint32 buffSpell = SelectBestSpell(SPELL_TYPE_BUFF);
                if (buffSpell)
                {
                    CastSpell(buffSpell);
                }
            }
            break;
            
        case COMBAT_STATE_ENGAGING:
        case COMBAT_STATE_FIGHTING:
            // 战斗状态
            if (!_currentTarget)
            {
                _currentTarget = SelectTarget();
            }
            
            if (_currentTarget)
            {
                // 优先治疗
                if (ShouldHeal())
                {
                    uint32 healSpell = SelectBestSpell(SPELL_TYPE_HEAL);
                    if (healSpell)
                    {
                        CastSpell(healSpell, _player);
                        break;
                    }
                }
                
                // 攻击
                uint32 damageSpell = SelectBestSpell(SPELL_TYPE_DAMAGE);
                if (damageSpell)
                {
                    CastSpell(damageSpell, _currentTarget);
                }
            }
            break;
            
        case COMBAT_STATE_FLEEING:
            // 逃跑状态
            if (ShouldHeal())
            {
                uint32 healSpell = SelectBestSpell(SPELL_TYPE_HEAL);
                if (healSpell)
                {
                    CastSpell(healSpell, _player);
                }
            }
            break;
            
        default:
            break;
    }
}

// 辅助函数实现
bool CombatAI::HasSpell(uint32 spellId)
{
    return _player && _player->HasSpell(spellId);
}

bool CombatAI::IsSpellReady(uint32 spellId)
{
    auto it = _spellCooldowns.find(spellId);
    return it == _spellCooldowns.end();
}

void CombatAI::SetSpellCooldown(uint32 spellId, uint32 cooldown)
{
    _spellCooldowns[spellId] = cooldown;
}

uint32 CombatAI::GetHealthPercentage()
{
    if (!_player)
        return 0;
    
    return _player->GetHealthPct();
}

uint32 CombatAI::GetManaPercentage()
{
    if (!_player)
        return 0;
    
    return _player->GetPowerPct(POWER_MANA);
}

bool CombatAI::HasEnoughMana(uint32 spellId)
{
    if (!_player)
        return false;
    
    const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
    if (!spellInfo)
        return false;
    
    return _player->GetPower(POWER_MANA) >= spellInfo->ManaCost;
}

bool CombatAI::IsInSpellRange(Unit* target, uint32 spellId)
{
    if (!target || !_player)
        return false;
    
    const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
    if (!spellInfo)
        return false;
    
    float range = spellInfo->GetMaxRange();
    return _player->GetDistance(target) <= range;
}

float CombatAI::GetOptimalRange(Unit* target)
{
    if (!_player)
        return COMBAT_MELEE_RANGE;

    // 根据职业确定最佳攻击范围
    uint8 playerClass = _player->getClass();

    switch (playerClass)
    {
        case CLASS_MAGE:
        case CLASS_WARLOCK:
        case CLASS_PRIEST:
            // 法系职业使用远程攻击
            return COMBAT_RANGED_RANGE;

        case CLASS_HUNTER:
            // 猎人优先使用远程攻击
            return COMBAT_RANGED_RANGE;

        case CLASS_WARRIOR:
        case CLASS_PALADIN:
        case CLASS_DEATH_KNIGHT:
            // 近战职业
            return COMBAT_MELEE_RANGE;

        case CLASS_ROGUE:
            // 盗贼也是近战职业，5码以内
            return COMBAT_MELEE_RANGE;

        case CLASS_SHAMAN:
        case CLASS_DRUID:
            // 萨满和德鲁伊可近可远，默认近战
            return COMBAT_MELEE_RANGE;

        default:
            return COMBAT_MELEE_RANGE;
    }
}

void CombatAI::AddTarget(Unit* target)
{
    if (!target)
        return;
    
    CombatTarget combatTarget;
    combatTarget.guid = target->GetGUID();
    combatTarget.unit = target;
    combatTarget.distance = _player->GetDistance(target);
    combatTarget.priority = static_cast<TargetPriority>(CalculateTargetPriority(target) / 20);
    combatTarget.lastAttackTime = getMSTime();
    combatTarget.isPlayer = target->IsPlayer();
    
    _targetList.push_back(combatTarget);
}

void CombatAI::ClearTargets()
{
    _targetList.clear();
}

void CombatAI::Flee()
{
    if (!_player)
        return;
    
    // 简单的逃跑逻辑：远离最近的敌人
    Unit* nearestEnemy = FindNearestEnemy();
    if (nearestEnemy)
    {
        float angle = nearestEnemy->GetAngle(_player) + M_PI;
        float x = _player->GetPositionX() + cos(angle) * 20.0f;
        float y = _player->GetPositionY() + sin(angle) * 20.0f;
        float z = _player->GetPositionZ();
        
        _player->GetMotionMaster()->Clear();
        _player->GetMotionMaster()->MovePoint(0, x, y, z);
        
        LogMovement("逃跑");
    }
}

Unit* CombatAI::FindNearestEnemy()
{
    Unit* nearest = nullptr;
    float minDistance = 999.0f;
    
    for (const auto& target : _targetList)
    {
        if (target.unit && target.unit->IsAlive())
        {
            float distance = _player->GetDistance(target.unit);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = target.unit;
            }
        }
    }
    
    return nearest;
}

void CombatAI::LogCombatAction(const std::string& action, Unit* target)
{
    if (target)
    {
        LOG_DEBUG("combat", "玩家 {} 战斗动作: {} (目标: {})", 
                 _player->GetName(), action, target->GetName());
    }
    else
    {
        LOG_DEBUG("combat", "玩家 {} 战斗动作: {}", _player->GetName(), action);
    }
}

void CombatAI::LogSpellCast(uint32 spellId, Unit* target)
{
    const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
    std::string spellName = spellInfo ? spellInfo->SpellName[0] : "Unknown";
    
    if (target)
    {
        LOG_DEBUG("combat", "玩家 {} 施放法术: {} (目标: {})", 
                 _player->GetName(), spellName, target->GetName());
    }
    else
    {
        LOG_DEBUG("combat", "玩家 {} 施放法术: {}", _player->GetName(), spellName);
    }
}

void CombatAI::LogMovement(const std::string& action)
{
    LOG_DEBUG("combat", "玩家 {} 移动: {}", _player->GetName(), action);
}

uint32 CombatAI::SelectBestSpell(SpellType type)
{
    if (!_player)
        return 0;

    // 根据类型选择对应的技能列表
    std::vector<uint32>* spellList = nullptr;
    switch (type)
    {
        case SPELL_TYPE_DAMAGE:
            spellList = &_damageSpells;
            break;
        case SPELL_TYPE_HEAL:
            spellList = &_healSpells;
            break;
        case SPELL_TYPE_BUFF:
            spellList = &_buffSpells;
            break;
        case SPELL_TYPE_DEBUFF:
            spellList = &_debuffSpells;
            break;
        case SPELL_TYPE_CONTROL:
            spellList = &_controlSpells;
            break;
        default:
            spellList = &_damageSpells;
            break;
    }

    if (!spellList || spellList->empty())
        return 0;

    // 从可用技能中选择最佳的
    for (uint32 spellId : *spellList)
    {
        if (CanCastSpell(spellId))
        {
            // 检查距离（如果有目标）
            if (_currentTarget && !IsInSpellRange(_currentTarget, spellId))
                continue;

            return spellId;
        }
    }

    return 0;
}

void CombatAI::ClassifySpell(uint32 spellId, const SpellInfo* spellInfo)
{
    if (!spellInfo)
        return;

    // 根据技能效果分类
    bool isDamage = false;
    bool isHeal = false;
    bool isBuff = false;
    bool isDebuff = false;
    bool isControl = false;
    bool isDefensive = false;

    // 分析技能效果
    for (uint8 i = 0; i < MAX_SPELL_EFFECTS; ++i)
    {
        uint32 effect = spellInfo->Effects[i].Effect;

        switch (effect)
        {
            case SPELL_EFFECT_SCHOOL_DAMAGE:
            case SPELL_EFFECT_WEAPON_DAMAGE:
            case SPELL_EFFECT_WEAPON_DAMAGE_NOSCHOOL:
            case SPELL_EFFECT_NORMALIZED_WEAPON_DMG:
                isDamage = true;
                break;

            case SPELL_EFFECT_HEAL:
            case SPELL_EFFECT_HEAL_MAX_HEALTH:
                isHeal = true;
                break;

            case SPELL_EFFECT_APPLY_AURA:
                {
                    uint32 aura = spellInfo->Effects[i].ApplyAuraName;
                    if (aura == SPELL_AURA_MOD_STAT || aura == SPELL_AURA_MOD_INCREASE_HEALTH ||
                        aura == SPELL_AURA_MOD_INCREASE_ENERGY || aura == SPELL_AURA_MOD_DAMAGE_DONE)
                    {
                        isBuff = true;
                    }
                    else if (aura == SPELL_AURA_PERIODIC_DAMAGE || aura == SPELL_AURA_MOD_DECREASE_SPEED)
                    {
                        isDebuff = true;
                    }
                    else if (aura == SPELL_AURA_MOD_STUN || aura == SPELL_AURA_MOD_FEAR ||
                             aura == SPELL_AURA_MOD_CHARM || aura == SPELL_AURA_MOD_ROOT)
                    {
                        isControl = true;
                    }
                    else if (aura == SPELL_AURA_MOD_RESISTANCE || aura == SPELL_AURA_DAMAGE_SHIELD)
                    {
                        isDefensive = true;
                    }
                }
                break;

            case SPELL_EFFECT_CHARGE:
            case SPELL_EFFECT_KNOCK_BACK:
                isControl = true;
                break;

            default:
                break;
        }
    }

    // 根据分析结果添加到对应列表
    if (isDamage)
        _damageSpells.push_back(spellId);
    if (isHeal)
        _healSpells.push_back(spellId);
    if (isBuff)
        _buffSpells.push_back(spellId);
    if (isDebuff)
        _debuffSpells.push_back(spellId);
    if (isControl)
        _controlSpells.push_back(spellId);
    if (isDefensive)
        _defensiveSpells.push_back(spellId);
}

void CombatAI::InitializeSpells()
{
    // 基础实现，子类可以重写
    LoadClassSpells();
}

void CombatAI::LoadClassSpells()
{
    // 技能学习由技能管理器在登录时处理，这里只需要加载已学技能
    if (!_player)
        return;

    LoadLearnedSpells();
    LOG_DEBUG("combat", "为机器人 {} 加载已学技能完成", _player->GetName());
}

void CombatAI::LoadLearnedSpells()
{
    if (!_player)
        return;

    // 清空现有技能列表
    _damageSpells.clear();
    _healSpells.clear();
    _buffSpells.clear();
    _debuffSpells.clear();
    _controlSpells.clear();
    _defensiveSpells.clear();

    LOG_DEBUG("combat", "开始为机器人 {} 分析已学技能", _player->GetName());

    // 遍历玩家已学的技能
    PlayerSpellMap const& spellMap = _player->GetSpellMap();
    for (auto const& spellPair : spellMap)
    {
        uint32 spellId = spellPair.first;
        PlayerSpell const* playerSpell = spellPair.second;

        // 跳过被动技能和已删除的技能
        if (playerSpell->State == PLAYERSPELL_REMOVED || !playerSpell->Active)
            continue;

        const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
        if (!spellInfo)
            continue;

        // 跳过被动技能
        if (spellInfo->IsPassive())
            continue;

        // 根据技能效果分类
        ClassifySpell(spellId, spellInfo);
    }

    LOG_DEBUG("combat", "机器人 {} 技能分类完成: 伤害({}) 治疗({}) 增益({}) 减益({}) 控制({}) 防御({})",
             _player->GetName(), _damageSpells.size(), _healSpells.size(), _buffSpells.size(),
             _debuffSpells.size(), _controlSpells.size(), _defensiveSpells.size());
}



// ClassCombatAI 实现
void ClassCombatAI::UseClassAbilities()
{
    // 基础实现，子类可以重写
}

void ClassCombatAI::HandleClassSpecificSituation()
{
    // 基础实现，子类可以重写
}

bool ClassCombatAI::CanUseClassFeature(const std::string& feature)
{
    // 基础实现，子类可以重写
    return false;
}

void ClassCombatAI::InitializeClassSpells()
{
    // 基础实现，子类可以重写
}

void ClassCombatAI::UpdateClassResources()
{
    // 基础实现，子类可以重写
}

void ClassCombatAI::HandleClassCombos()
{
    // 基础实现，子类可以重写
}

// 缺失的CombatAI函数实现
void CombatAI::MoveToPosition(float x, float y, float z)
{
    if (!_player)
        return;

    _player->GetMotionMaster()->Clear();
    _player->GetMotionMaster()->MovePoint(0, x, y, z);

    LogMovement("移动到指定位置");
}

bool CombatAI::IsInRange(Unit* target, float range)
{
    if (!target || !_player)
        return false;

    return _player->GetDistance(target) <= range;
}

void CombatAI::HandleEmergency()
{
    if (!_player)
        return;

    // 紧急情况处理
    if (ShouldFlee())
    {
        Flee();
        LOG_DEBUG("combat", "玩家 {} 进入紧急逃跑模式", _player->GetName());
    }
    else if (ShouldHeal())
    {
        uint32 healSpell = SelectBestSpell(SPELL_TYPE_HEAL);
        if (healSpell)
        {
            CastSpell(healSpell, _player);
            LOG_DEBUG("combat", "玩家 {} 紧急治疗", _player->GetName());
        }
    }
}
