#ifndef MOD_PBOT_LOADER_H
#define MOD_PBOT_LOADER_H

// 声明所有 AddSC_ 函数

// 从 Faker.cpp
void AddSC_FakerLogin();
void AddSC_FakerMovement();
void AddSC_FakerDatabaseScript();

// 从BotOpcodeHandler.cpp
void AddSC_BotOpcodeHandler();

// 从BotInteractionOpcodeHandler.cpp
void AddSC_BotInteractionOpcodeHandler();

// 从BotDuelPlayerScript.cpp
void AddSC_BotDuelPlayerScript();

// 从BotControlCommands.cpp
void AddSC_BotControlCommands();

// 从FakePlayers.cpp
void AddSC_FakePlayerScript();

// 从YGbotTalentCommand.cpp
void AddSC_YGbotTalentCommand();

// 从YGbotTalentManager.cpp
void AddSC_YGbotTalentManager();

// 从YGbotGlyphManager.cpp
void AddSC_YGbotGlyphManager();

// 从YGbotGlyphCommands.cpp
void AddSC_YGbotGlyphCommands();

// 从非脚本文件（包含空实现）
void AddSC_BotEventSystem();
void AddSC_BotEventHandlers();
void AddSC_BotBehaviorEngine();
void AddSC_BotDeathHandler();

// 从MovementSystemScript.cpp
void AddSC_BotMovementSystem();

// 加载所有脚本 - AzerothCore 模块系统要求的函数名
void Addmod_ygbotScripts()
{
    AddSC_FakerLogin();
    AddSC_FakerMovement();
    AddSC_FakerDatabaseScript();
    AddSC_BotOpcodeHandler();
    AddSC_BotInteractionOpcodeHandler();
    AddSC_BotDuelPlayerScript();
    AddSC_BotControlCommands();
    AddSC_FakePlayerScript();
    AddSC_YGbotTalentCommand();
    AddSC_YGbotTalentManager();
    AddSC_YGbotGlyphManager();
    AddSC_YGbotGlyphCommands();
    AddSC_BotEventSystem();
    AddSC_BotEventHandlers();
    AddSC_BotBehaviorEngine();
    AddSC_BotDeathHandler();
    AddSC_BotMovementSystem();
}

#endif // MOD_PBOT_LOADER_H