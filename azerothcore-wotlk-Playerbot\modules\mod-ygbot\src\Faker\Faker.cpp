#include "Faker.h"
#include "BotEventSystem.h"
//#include "../String/myString.h"
//#include "../Switch/Switch.h"
#include "Battleground.h"
#include "BattlegroundMgr.h"
#include "Waypoints/WaypointMgr.h"
#include "Util.h"
#include "World.h"
#include "WorldPacket.h"
#include "ScriptMgr.h"
#include "WorldSession.h"
#include "CharacterDatabase.h"
#include "QueryHolder.h"
#include "Config.h"
#include "../PlayerPatch.h"
#include "SpellMgr.h"
#include "SpellInfo.h"
#include "Player.h"
#include "Unit.h"
#include "Creature.h"
#include "Cell.h"
#include "CellImpl.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include <algorithm>
#include <cctype>
#include "PathGenerator.h"
#include "MoveSplineInit.h"
#include "Common.h"
#include "BotEventSystem.h"
#include "BotEventHandlers.h"
#include "BotBehaviorEngine.h"
#include "BotDeathHandler.h"

std::unordered_map<uint32, bool> FakerMap;
std::vector<WorldSession*> FakerSessions;
std::unordered_map<uint32, VirtualAccountInfo> VirtualAccounts;

void Faker::Load()
{
	FakerMap.clear();
    virtualAccountMap.clear();
    nextVirtualAccountId = 100000; // 设置初始虚拟账号ID
    
    // 从配置文件加载机器人账号前缀，与mod-playerbots-liyunfan保持一致
    randomBotAccountPrefix = sConfigMgr->GetOption<std::string>("AiPlayerbot.RandomBotAccountPrefix", "rndbot");
    LOG_INFO("server", "机器人账号前缀: {}", randomBotAccountPrefix);

	QueryResult result;

	// 直接从characters表加载可用角色
	if (result = CharacterDatabase.Query("SELECT guid FROM characters WHERE online = 0"))
	{
		do
		{
			uint32 guid = result->Fetch()[0].Get<uint32>();
			FakerMap.insert(std::make_pair(guid, false));
		} while (result->NextRow());
	}

    // 加载虚拟账号映射
    if (result = WorldDatabase.Query("SELECT original_account, virtual_account FROM ygbot_accounts"))
    {
        do
        {
            Field* fields = result->Fetch();
            uint32 originalAccount = fields[0].Get<uint32>();
            uint32 virtualAccount = fields[1].Get<uint32>();
            virtualAccountMap[originalAccount] = virtualAccount;
            
            // 更新下一个可用的虚拟账号ID
            if (virtualAccount >= nextVirtualAccountId)
                nextVirtualAccountId = virtualAccount + 1;
        } while (result->NextRow());
    }
    
    // 查找现有的rndbot账号，确保不会与现有账号冲突
    if (result = LoginDatabase.Query("SELECT id, username FROM account WHERE username LIKE '{}%'", randomBotAccountPrefix.c_str()))
    {
        do
        {
            Field* fields = result->Fetch();
            uint32 accountId = fields[0].Get<uint32>();
            std::string username = fields[1].Get<std::string>();
            
            // 如果找到比当前nextVirtualAccountId更大的ID，更新nextVirtualAccountId
            if (accountId >= nextVirtualAccountId)
                nextVirtualAccountId = accountId + 1;
                
           /* LOG_INFO("server", "找到现有机器人账号: {} (ID: {})", username.c_str(), accountId);*/
        } while (result->NextRow());
    }
    
    LOG_INFO("server", "虚拟账号系统初始化完成，下一个可用ID: {}", nextVirtualAccountId);

    // 初始化事件系统
    InitializeEventSystem();
}

void Faker::Add(bool ingnoreLimit)
{
	uint32 count = 0;

	for (auto itr = FakerMap.begin(); itr != FakerMap.end(); itr++)
		if (itr->second)
			count++;

	uint32 maxPlayers = sConfigMgr->GetOption<uint32>("YGbot.MaxPlayers", 30);
	if (!ingnoreLimit && count >= maxPlayers)
		return;
	
	// 尝试从FakerMap中找到一个未在线的角色
	for (auto itr = FakerMap.begin(); itr != FakerMap.end(); itr++)
	{
		if (itr->second)
			continue;

		AddFaker(itr->first);
		return;
	}
	
	// 如果FakerMap为空或所有机器人都已上线，尝试从数据库加载一个新角色
	QueryResult result = CharacterDatabase.Query("SELECT guid FROM characters WHERE online = 0 LIMIT 1");
	if (result)
	{
		uint32 guid = result->Fetch()[0].Get<uint32>();
		FakerMap[guid] = false; // 添加到FakerMap
		AddFaker(guid);
	}
	else
	{
		LOG_INFO("server", "没有可用的角色可添加为机器人");
	}
}

// 获取或创建虚拟账号
uint32 Faker::GetOrCreateVirtualAccount(uint32 originalAccount)
{
    // 检查是否已经有对应的虚拟账号
    auto itr = virtualAccountMap.find(originalAccount);
    if (itr != virtualAccountMap.end())
        return itr->second;
    
    // 创建新的虚拟账号
    uint32 virtualAccount = nextVirtualAccountId++;
    
    // 检查是否存在冲突
    while (true) 
    {
        // 构建账号名称
        std::ostringstream out;
        out << randomBotAccountPrefix << virtualAccount;
        std::string accountName = out.str();
        
        // 检查账号是否已存在
        LoginDatabasePreparedStatement* stmt = LoginDatabase.GetPreparedStatement(LOGIN_GET_ACCOUNT_ID_BY_USERNAME);
        stmt->SetData(0, accountName);
        PreparedQueryResult result = LoginDatabase.Query(stmt);
        
        // 如果账号已存在，增加ID并重试
        if (result)
        {
            virtualAccount++;
            continue;
        }
        
        // 账号不存在，可以使用这个ID
        break;
    }
    
    // 创建新的虚拟账号
    std::ostringstream out;
    out << randomBotAccountPrefix << virtualAccount;
    std::string accountName = out.str();
    std::string password = accountName; // 简单密码

    // 从原始账号复制一些基本数据
    QueryResult originalAccountData = LoginDatabase.Query("SELECT expansion, locale FROM account WHERE id = {}", originalAccount);
    if (originalAccountData)
    {
        Field* fields = originalAccountData->Fetch();
        uint8 expansion = fields[0].Get<uint8>();
        std::string locale = fields[1].Get<std::string>();
        
        // 创建账号 - AzerothCore的AccountMgr::CreateAccount只接受用户名和密码
        AccountMgr::CreateAccount(accountName, password);
        
        // 更新账号的其他信息
        LoginDatabase.Execute("UPDATE account SET expansion = {}, locale = '{}' WHERE username = '{}'", 
                             expansion, locale, accountName);
        
        // 更新账号ID为我们预设的ID
        LoginDatabase.Execute("UPDATE account SET id = {} WHERE username = '{}'", virtualAccount, accountName);
    }
    else
    {
        // 如果无法获取原始账号数据，则使用默认值
        AccountMgr::CreateAccount(accountName, password);
        
        // 更新账号ID为我们预设的ID
        LoginDatabase.Execute("UPDATE account SET id = {} WHERE username = '{}'", virtualAccount, accountName);
    }
    
    virtualAccountMap[originalAccount] = virtualAccount;
    
    // 保存到数据库，使用WorldDatabase
    WorldDatabase.Execute("INSERT INTO ygbot_accounts (original_account, virtual_account) VALUES ({}, {})",
        originalAccount, virtualAccount);
    
    LOG_INFO("server", "为原始账号{}创建了虚拟账号{} (用户名: {})", originalAccount, virtualAccount, accountName);
    
    return virtualAccount;
}

// 检查是否为虚拟账号
bool Faker::IsVirtualAccount(uint32 accountId)
{
    for (const auto& pair : virtualAccountMap)
    {
        if (pair.second == accountId)
            return true;
    }
    return false;
}

// 获取原始账号
uint32 Faker::GetOriginalAccount(uint32 virtualAccount)
{
    for (const auto& pair : virtualAccountMap)
    {
        if (pair.second == virtualAccount)
            return pair.first;
    }
    return 0; // 未找到
}

// 清理虚拟账号
void Faker::CleanupVirtualAccounts()
{
    // 移除不再使用的虚拟账号
    for (auto itr = VirtualAccounts.begin(); itr != VirtualAccounts.end();)
    {
        if (!itr->second.isOnline)
        {
            itr = VirtualAccounts.erase(itr);
        }
        else
        {
            ++itr;
        }
    }
}

void Faker::HandleFakerLoginCallback(FakerLoginQueryHolder const& holder)
{
    uint32 botAccountId = holder.GetAccountId();
    ObjectGuid playerGuid = holder.GetGuid();
    
    // 创建会话，与mod-playerbots-liyunfan一致
    WorldSession* botSession = new WorldSession(botAccountId, "", nullptr, SEC_PLAYER, EXPANSION_WRATH_OF_THE_LICH_KING,
                                               time_t(0), sWorld->GetDefaultDbcLocale(), 0, false, false, 0, true);

    botSession->HandlePlayerLoginFromDB(holder);  // 处理登录

    Player* bot = botSession->GetPlayer();
    if (!bot)
    {
        // 登录失败
        LOG_ERROR("server", "机器人登录失败，GUID: {}", playerGuid.GetCounter());
        botSession->LogoutPlayer(true);
        delete botSession;
        botLoading.erase(playerGuid);
        return;
    }

    // 登录成功
    LOG_INFO("server", "机器人 {} (GUID: {}) 登录成功", bot->GetName(), playerGuid.GetCounter());
    
    // 更新虚拟账号状态
    auto itr = VirtualAccounts.find(playerGuid.GetCounter());
    if (itr != VirtualAccounts.end())
    {
        itr->second.isOnline = true;
    }
    
    // 检查是否死亡，如果死亡则启动智能复活系统
    if (!bot->IsAlive())
    {
        LOG_INFO("server", "机器人 {} 处于死亡状态，启动智能复活系统", bot->GetName());

        // 简化复活处理 - 移除了智能复活系统
        bot->ResurrectPlayer(1.0f);
        bot->SpawnCorpseBones();

        LOG_INFO("server", "机器人 {} 已复活", bot->GetName());
    }
    else
    {
        // 简化处理 - 移除了智能系统管理
    }
    
    // 更新FakerMap
    auto mapItr = FakerMap.find(playerGuid.GetCounter());
    if (mapItr != FakerMap.end())
    {
        mapItr->second = true;
    }
    else
    {
        LOG_ERROR("server", "机器人 {} 在FakerMap中未找到", bot->GetName());
    }
    
    // 添加到会话列表
    FakerSessions.push_back(botSession);
    
    // 调用登录事件
    OnLogin(bot);
    
    // 从加载列表中移除
    botLoading.erase(playerGuid);
}

void AddFakerAsync(int guid)
{
	LOG_INFO("server", "开始添加机器人 GUID: {}", guid);
	
	// 检查是否已经在加载中
	if (sFaker->botLoading.find(ObjectGuid::Create<HighGuid::Player>(guid)) != sFaker->botLoading.end())
	{
	    LOG_INFO("server", "机器人 GUID: {} 已经在加载中", guid);
	    return;
	}
	
	// 检查是否已经在线
	for (auto itr = FakerSessions.begin(); itr != FakerSessions.end(); ++itr)
	{
	    WorldSession* s = *itr;
	    if (s && s->GetPlayer() && s->GetPlayer()->GetGUID().GetCounter() == guid)
	    {
	        LOG_INFO("server", "机器人 GUID: {} 已经在线", guid);
	        return;
	    }
	}
	
	QueryResult result = CharacterDatabase.Query("SELECT account FROM characters WHERE guid = {}", guid);

	if (!result)
	{
		LOG_ERROR("server", "无法找到GUID为{}的角色", guid);
		return;
	}

	uint32 originalAccount = result->Fetch()[0].Get<uint32>();
    
    // 使用虚拟账号系统
    uint32 virtualAccount = sFaker->GetOrCreateVirtualAccount(originalAccount);
    
    // 更新VirtualAccounts映射
    VirtualAccountInfo accountInfo;
    accountInfo.originalAccount = originalAccount;
    accountInfo.virtualAccount = virtualAccount;
    accountInfo.isOnline = false;
    VirtualAccounts[guid] = accountInfo;

    // 标记为正在加载
    ObjectGuid playerGuid = ObjectGuid::Create<HighGuid::Player>(guid);
    sFaker->botLoading.insert(playerGuid);

    // 创建自定义的LoginQueryHolder，与mod-playerbots-liyunfan一致
    std::shared_ptr<FakerLoginQueryHolder> holder = std::make_shared<FakerLoginQueryHolder>(0, originalAccount, playerGuid);
	if (!holder->Initialize())
	{
		LOG_ERROR("server", "初始化查询持有者失败，GUID: {}", guid);
		sFaker->botLoading.erase(playerGuid);
		return;
	}

    // 使用与mod-playerbots-liyunfan一致的回调方式
    sWorld->AddQueryHolderCallback(CharacterDatabase.DelayQueryHolder(holder))
        .AfterComplete([](SQLQueryHolderBase const& holder)
                        { sFaker->HandleFakerLoginCallback(static_cast<FakerLoginQueryHolder const&>(holder)); });
}

void AddFaker(int i)
{
    // 使用异步任务代替Windows线程
    std::thread asyncThread(AddFakerAsync, i);
    asyncThread.detach();
}

void Faker::Remove(uint64 guid)
{
	// ❌ 已禁用：移动计时器现在由BotBehaviorEngine管理
	/*
	// 清理移动计时器
	if (moveTimers.find(guid) != moveTimers.end())
	{
		moveTimers.erase(guid);
	}
	*/
	
	// 清理上次移动时间
	if (lastMoveTimes.find(guid) != lastMoveTimes.end())
	{
		lastMoveTimes.erase(guid);
	}

	for (auto itr = FakerSessions.begin(); itr != FakerSessions.end();)
	{
		WorldSession* s = *itr;

		if (s && s->GetPlayer() && s->GetPlayer()->GetGUID().GetCounter() == guid)
		{
			// 检查是否死亡，如果死亡则由智能系统处理
			Player* player = s->GetPlayer();
			if (player && !player->IsAlive())
			{
				LOG_INFO("server", "机器人 {} 处于死亡状态，由智能复活系统管理", player->GetName());

				// 简化死亡处理 - 移除了智能系统

				// 不要移除会话，让智能系统处理复活
				LOG_INFO("server", "机器人 {} 已交由智能复活系统处理，取消移除", player->GetName());
				return;
			}
            
            // 更新虚拟账号状态
            auto accountItr = VirtualAccounts.find(guid);
            if (accountItr != VirtualAccounts.end())
            {
                accountItr->second.isOnline = false;
            }
			
			// 调用登出事件
			OnLogout(s->GetPlayer());
			
			// 登出玩家并删除会话
			s->LogoutPlayer(true);
			delete s;
			itr = FakerSessions.erase(itr);
		}
		else
			itr++;
	}
}
		
		
void Faker::UpdateAllSessions(uint32 diff)
{
    // 只在调试模式下记录会话数量，减少日志输出
    static uint32 logTimer = 0;
    logTimer += diff;
    if (logTimer >= 60000) // 每60秒记录一次
    {
        LOG_INFO("server", "当前活跃机器人会话数: {}", FakerSessions.size());
        logTimer = 0;
    }
    
    ///- Then send an update signal to remaining ones
    for (auto itr = FakerSessions.begin(); itr != FakerSessions.end(); itr++)
    {
        ///- and remove not active sessions from the list
        WorldSession* s = *itr;
        if (s)
        {
            WorldSessionFilter updater(s);
            s->Update(diff, updater);

            if (Player* player = s->GetPlayer())
            {
                if (!player->IsAlive())
                {
                    LOG_DEBUG("server", "机器人 {} 处于死亡状态，由智能复活系统处理", player->GetName());

                    // 简化死亡处理 - 移除了智能系统

                    // 不要移除会话，让智能系统处理复活
                    // sFaker->Remove(player->GetGUID().GetCounter());
                    // return;
                }

                if (player->IsBeingTeleportedFar())
                {
                    LOG_INFO("server", "机器人 {} 正在远距离传送", player->GetName());
                    s->HandleMoveWorldportAck();
                }

                // 确保机器人被标记为机器人
                PlayerPatch::SetIsFaker(player, true);
                
                // 更新机器人行为
                Update(player, diff);
            }
        }
    }
}

void Faker::Update(Player* faker, uint32 diff)
{
	if (!PlayerPatch::GetIsFaker(faker))
		return;

	if (faker->IsBeingTeleportedNear())
	{
		WorldPacket p = WorldPacket(MSG_MOVE_TELEPORT_ACK, 8 + 4 + 4);
		p.appendPackGUID(faker->GetGUID().GetRawValue());
		p << (uint32)0;
		p << (uint32)time(0);
		faker->GetSession()->HandleMoveTeleportAck(p);
	}
	else if (faker->IsInWorld())
	{
		// 通过事件系统处理所有逻辑 - 遵循 FakePlayers → Faker → 各个系统 的架构

		// 更新交互响应任务调度器 (优先级最高)
		g_interactionScheduler.Update(diff);

		// 更新死亡系统 (优先级最高)
		if (sBotDeathHandler)
		{
			sBotDeathHandler->UpdateBot(faker, diff);
		}

		// 简化更新 - 移除了智能玩家系统

		// 简化更新 - 移除了战斗AI系统

		// ✅ 增强的战斗逻辑：持续战斗直到目标死亡，自动寻找新目标
		if (faker->IsInCombat())
		{
			Unit* currentTarget = faker->GetSelectedUnit();

			// 检查当前目标是否有效
			bool needNewTarget = false;
			if (!currentTarget)
			{
				needNewTarget = true;
				LOG_DEBUG("server", "机器人 {} 在战斗中但没有目标，需要寻找新目标", faker->GetName());
			}
			else if (currentTarget->isDead())
			{
				needNewTarget = true;
				LOG_INFO("server", "机器人 {} 的目标 {} 已死亡，寻找新目标",
						 faker->GetName(), currentTarget->GetName());
				faker->SetTarget(ObjectGuid::Empty);
			}
			else if (!IsValidCombatTarget(faker, currentTarget))
			{
				needNewTarget = true;
				LOG_DEBUG("server", "机器人 {} 的目标 {} 不再有效，寻找新目标",
						 faker->GetName(), currentTarget->GetName());
				faker->SetTarget(ObjectGuid::Empty);
			}

			// 如果需要新目标，自动寻找
			if (needNewTarget)
			{
				Unit* newTarget = sFaker->FindBestCombatTarget(faker);
				if (newTarget)
				{
					//LOG_INFO("server", "机器人 {} 找到新战斗目标 {}",
					//		 faker->GetName(), newTarget->GetName());

					// 设置新目标并开始攻击
					faker->SetTarget(newTarget->GetGUID());
					faker->Attack(newTarget, true);

					// 简化战斗处理 - 移除了战斗AI
				}
				else
				{
					LOG_DEBUG("server", "机器人 {} 在战斗中但找不到有效目标", faker->GetName());
				}
			}
			else
			{
				// 当前目标有效，确保机器人正在攻击
				if (!faker->GetVictim() || faker->GetVictim() != currentTarget)
				{
					LOG_DEBUG("server", "机器人 {} 重新开始攻击目标 {}",
							 faker->GetName(), currentTarget->GetName());
					faker->Attack(currentTarget, true);
				}
			}
		}

		// 添加UPDATE事件频率控制，避免过于频繁的事件触发
		uint64 guid = faker->GetGUID().GetCounter();
		static std::unordered_map<uint64, uint32> lastUpdateEventTime;
		uint32 currentTime = getMSTime();

		if (lastUpdateEventTime.find(guid) == lastUpdateEventTime.end() ||
			(currentTime - lastUpdateEventTime[guid]) >= 2000) // 2秒触发一次UPDATE事件
		{
			lastUpdateEventTime[guid] = currentTime;
			// 触发更新事件，让事件系统处理移动等逻辑
			sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_UPDATE, faker);
		}
	}
}

// ✅ 寻找最佳战斗目标的辅助方法
Unit* Faker::FindBestCombatTarget(Player* bot)
{
	if (!bot)
		return nullptr;

	std::list<Unit*> targets;
	Acore::AnyUnfriendlyUnitInObjectRangeCheck u_check(bot, bot, 20.0f);
	Acore::UnitListSearcher<Acore::AnyUnfriendlyUnitInObjectRangeCheck> searcher(bot, targets, u_check);
	Cell::VisitAllObjects(bot, searcher, 20.0f);

	Unit* bestTarget = nullptr;
	float bestPriority = 0.0f;

	for (Unit* target : targets)
	{
		if (!target || target->isDead() || !sFaker->IsValidCombatTarget(bot, target))
			continue;

		float priority = sFaker->CalculateTargetPriority(bot, target);
		if (priority > bestPriority)
		{
			bestPriority = priority;
			bestTarget = target;
		}
	}

	return bestTarget;
}

// 计算目标优先级的辅助方法
float Faker::CalculateTargetPriority(Player* bot, Unit* target)
{
	if (!bot || !target)
		return 0.0f;

	float priority = 1.0f;
	float distance = bot->GetDistance(target);

	// ✅ 特殊处理：训练假人优先级
	if (Creature* creature = target->ToCreature())
	{
		std::string name = creature->GetName();
		std::transform(name.begin(), name.end(), name.begin(), ::tolower);

		if (name.find("training") != std::string::npos ||
			name.find("target") != std::string::npos ||
			name.find("dummy") != std::string::npos)
		{
			priority += 80.0f; // 训练假人有较高优先级，但低于攻击机器人的敌人
			LOG_DEBUG("server", "CalculateTargetPriority: 训练假人 {} 获得额外优先级", target->GetName());
		}
	}

	// 优先级1：正在攻击机器人的目标（最高优先级）
	if (target->GetVictim() == bot)
	{
		priority += 100.0f;
	}

	// 优先级2：正在攻击队友的目标
	if (Group* group = bot->GetGroup())
	{
		for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
		{
			Player* member = itr->GetSource();
			if (member && member != bot && target->GetVictim() == member)
			{
				priority += 50.0f;
				break;
			}
		}
	}

	// 优先级3：已经在战斗中的目标
	if (target->IsInCombat())
	{
		priority += 20.0f;
	}

	// 优先级4：距离越近优先级越高
	if (distance > 0.1f)
	{
		priority += (20.0f - distance) / 20.0f * 10.0f;
	}

	// 优先级5：生命值较低的目标（更容易击杀）
	float healthPct = target->GetHealthPct();
	if (healthPct < 50.0f)
	{
		priority += (50.0f - healthPct) / 50.0f * 5.0f;
	}

	return priority;
}

// ✅ 检查目标是否为有效的战斗目标（包括训练假人）
bool Faker::IsValidCombatTarget(Player* bot, Unit* target)
{
	if (!bot || !target)
		return false;

	// 检查目标是否死亡
	if (target->isDead())
		return false;

	// ✅ 检查是否为训练假人
	if (Creature* creature = target->ToCreature())
	{
		// 训练假人的Entry ID列表（常见的训练假人）
		std::vector<uint32> trainingDummyEntries = {
			2673,   // Target Dummy
			2674,   // Advanced Target Dummy
			12426,  // Masterwork Target Dummy
			16211,  // Apprentice's Training Dummy
			17578,  // Journeyman's Training Dummy
			31144,  // Scarlet Monastery Training Dummy
			32666,  // Scarlet Monastery Training Dummy
			32667,  // Scarlet Monastery Training Dummy
			46647,  // Training Dummy
			87317,  // Training Dummy
			87318,  // Training Dummy
			87761   // Training Dummy
		};

		uint32 entry = creature->GetEntry();
		for (uint32 dummyEntry : trainingDummyEntries)
		{
			if (entry == dummyEntry)
			{
				LOG_DEBUG("server", "IsValidCombatTarget: {} 是训练假人 (Entry: {})", target->GetName(), entry);
				return true; // 训练假人总是有效目标
			}
		}

		// 检查名称是否包含训练假人关键词
		std::string name = creature->GetName();
		std::transform(name.begin(), name.end(), name.begin(), ::tolower);

		bool isTrainingDummy = (name.find("training") != std::string::npos ||
								name.find("target") != std::string::npos ||
								name.find("dummy") != std::string::npos ||
								name.find("practice") != std::string::npos);

		if (isTrainingDummy)
		{
			LOG_DEBUG("server", "IsValidCombatTarget: {} 被识别为训练假人 (名称匹配)", target->GetName());
			return true;
		}
	}

	// 对于非训练假人，使用标准的攻击目标检查
	bool isValid = bot->IsValidAttackTarget(target);
	LOG_DEBUG("server", "IsValidCombatTarget: {} 标准检查结果: {}", target->GetName(), isValid);
	return isValid;
}

void Faker::OnLogin(Player* faker)
{
	PlayerPatch::SetIsFaker(faker, true);

	// 登录时的逻辑
	uint64 guid = faker->GetGUID().GetCounter();

	// ❌ 已禁用：移动计时器现在由BotBehaviorEngine管理
	/*
	// 初始化移动计时器，死亡的机器人不初始化移动
	if (faker->IsAlive())
	{
		// 活着的机器人延迟5秒后开始移动
		moveTimers[guid] = getMSTime() + 5000;
		SetLastMoveTime(guid, getMSTime());
		LOG_DEBUG("server", "机器人 {} 活着，5秒后开始移动", faker->GetName());
	}
	else
	{
		// 死亡的机器人不设置移动计时器
		moveTimers[guid] = UINT32_MAX; // 设置为最大值，表示不移动
		LOG_DEBUG("server", "机器人 {} 死亡，不设置移动计时器", faker->GetName());
	}
	*/

	// 检查是否死亡，如果死亡则启动智能复活系统
	if (!faker->IsAlive())
	{
		LOG_INFO("server", "机器人 {} 处于死亡状态，启动智能复活系统", faker->GetName());

		// 简化复活处理 - 移除了智能复活系统
		faker->ResurrectPlayer(1.0f);
		faker->SpawnCorpseBones();
	}

	// 触发登录事件
	sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_LOGIN, faker);

	// 立即触发一次移动
	if (faker->IsAlive())
	{
		// 获取当前位置
		float x = faker->GetPositionX();
		float y = faker->GetPositionY();
		float z = faker->GetPositionZ();

		// 随机选择一个方向和距离
		float angle = frand(0, 2 * M_PI);
		float dist = frand(5.0f, 15.0f);

		// 计算新位置
		float newX = x + dist * cos(angle);
		float newY = y + dist * sin(angle);
		float newZ = z;

		// 获取地面高度
		faker->UpdateAllowedPositionZ(newX, newY, newZ);

		// 随机走路或跑步
		faker->SetWalk(urand(0, 1) == 0);

		// 检查机器人是否可以移动
		if (faker->HasUnitState(UNIT_STATE_ROOT) ||
			faker->HasUnitState(UNIT_STATE_STUNNED) ||
			faker->HasUnitState(UNIT_STATE_CONFUSED) ||
			faker->HasUnitState(UNIT_STATE_FLEEING) ||
			faker->HasUnitState(UNIT_STATE_IN_FLIGHT))
		{
			LOG_INFO("server", "机器人 {} 由于状态限制无法移动", faker->GetName());
			return;
		}

		// 使用简单直接的移动方式，避免复杂的路径计算
		faker->GetMotionMaster()->Clear();
		faker->GetMotionMaster()->MovePoint(0, newX, newY, newZ);

		LOG_INFO("server", "机器人 {} 开始移动", faker->GetName());
	}
	else
	{
		LOG_INFO("server", "机器人 {} 未存活，无法移动", faker->GetName());
	}
}

void Faker::OnLogout(Player* faker)
{
	// 触发登出事件
	sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_LOGOUT, faker);

	// 登出时的逻辑
	uint64 guid = faker->GetGUID().GetCounter();

	// ❌ 已禁用：移动计时器现在由BotBehaviorEngine管理
	/*
	// 清理移动计时器
	if (moveTimers.find(guid) != moveTimers.end())
	{
		moveTimers.erase(guid);
	}
	*/

	// 清理上次移动时间
	if (lastMoveTimes.find(guid) != lastMoveTimes.end())
	{
		lastMoveTimes.erase(guid);
	}
}

void Faker::OnMove(Player* faker)
{
	// 触发移动事件
	sBotEventMgr->TriggerMoveEvent(BotEventType::BOT_EVENT_MOVE_START, faker,
		faker->GetPositionX(), faker->GetPositionY(), faker->GetPositionZ(),
		faker->GetOrientation(), faker->GetMapId(), faker->GetZoneId(), faker->GetAreaId());
}

void Faker::OnMoveToObject(Player* faker, Object* obj)
{
	// 移动到对象时的逻辑
	sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_OBJECT_INTERACT, faker);
}

void Faker::OnDetectObject(Player* faker, Object* obj)
{
	// 探测到对象时的逻辑
	sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_OBJECT_INTERACT, faker);
}

void Faker::OnEnterCombat(Player* faker, Unit* victim)
{
	// 触发战斗开始事件
	sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_COMBAT_START, faker, victim);
}

void Faker::OnUpdateZone(Player* faker)
{
	// 触发区域变更事件
	sBotEventMgr->TriggerMoveEvent(BotEventType::BOT_EVENT_ZONE_CHANGE, faker,
		faker->GetPositionX(), faker->GetPositionY(), faker->GetPositionZ(),
		faker->GetOrientation(), faker->GetMapId(), faker->GetZoneId(), faker->GetAreaId());
}

void Faker::OnKilled(Player* faker, Unit* killer)
{
	// 触发死亡事件
	sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_DEATH, faker, killer);
}

void Faker::OnKill(Player* faker, Unit* victim)
{
	// 触发击杀事件
	sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_KILL, faker, victim);
}

void Faker::OnRecvWhisper(Player* faker, Player* sender)
{
	// 触发私聊事件
	sBotEventMgr->TriggerChatEvent(BotEventType::BOT_EVENT_CHAT_WHISPER, faker, "", CHAT_MSG_WHISPER, LANG_COMMON, sender);
}

// ❌ 已禁用：随机移动判断现在由BotBehaviorEngine统一管理
/*
bool Faker::ShouldBotRandomMove(Player* bot)
{
	// 移动控制现在由BotBehaviorEngine统一管理
	// 这个函数已被重构到BotBehaviorEngine中
	return false;
}
*/

// ❌ 已禁用：安全随机移动现在由BotBehaviorEngine统一管理
/*
bool Faker::PerformSafeRandomMovement(Player* bot)
{
	// 移动执行现在由BotMovementManager统一管理
	// 这个函数已被重构到BotBehaviorEngine + BotMovementManager中
	return false;
}
*/

void Faker::OnEventStart(Player* faker, uint32 eventId)
{
	// 事件开始时的逻辑
	sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_CUSTOM, faker);
}

void Faker::OnAcceptQuest(Player* faker, uint32 questId)
{
	// 触发接受任务事件
	sBotEventMgr->TriggerQuestEvent(BotEventType::BOT_EVENT_QUEST_ACCEPT, faker, questId);
}

void Faker::OnCompeleteQuest(Player* faker, uint32 questId)
{
	// 完成任务时的逻辑
	sBotEventMgr->TriggerQuestEvent(BotEventType::BOT_EVENT_QUEST_COMPLETE, faker, questId);
}

void Faker::InitializeEventSystem()
{
    LOG_INFO("server", "Faker: 初始化机器人事件系统");

    // 初始化操作码映射器
    sBotOpcodeMapper->Initialize();

    // 注册所有事件处理器
    BotEventHandlerFactory::RegisterAllHandlers();

    // 初始化交互响应系统 (新增)
    LOG_INFO("server", "Faker: 初始化交互响应系统");
    AddSC_BotInteractionOpcodeHandler();

    // 初始化行为引擎 (如果编译有问题，可以临时注释掉)
    #ifdef ENABLE_BOT_BEHAVIOR_ENGINE
    sBotBehaviorEngine->Initialize();
    #endif

    // 简化初始化 - 移除了移动系统和智能玩家系统
    LOG_INFO("server", "Faker: 跳过移动系统和智能玩家系统初始化");

    // 初始化死亡系统 (关键！)
    LOG_INFO("server", "Faker: 初始化死亡系统");
    // BotDeathHandler是单例，不需要显式初始化，但需要确保可用
    if (sBotDeathHandler)
    {
        LOG_INFO("server", "Faker: 死亡系统初始化成功");
    }
    else
    {
        LOG_ERROR("server", "Faker: 死亡系统初始化失败！");
    }

    LOG_INFO("server", "Faker: 机器人事件系统初始化完成，处理器数量: {}", sBotEventMgr->GetHandlerCount());
}

void Faker::ShutdownEventSystem()
{
    LOG_INFO("server", "Faker: 关闭机器人事件系统");

    // 注销所有事件处理器
    BotEventHandlerFactory::UnregisterAllHandlers();

    // 简化关闭 - 移除了移动系统

    // 禁用事件系统
    sBotEventMgr->SetEnabled(false);

    LOG_INFO("server", "Faker: 机器人事件系统已关闭");
}

void Faker::UpdateTalentDisplay(Player* player)
{
    if (!player)
        return;

    LOG_DEBUG("server", "Faker: 更新玩家 {} 的天赋显示", player->GetName());

    // 发送天赋信息数据
    player->SendTalentsInfoData(false);

    // 简单的状态更新，不需要复杂的UI操作
    LOG_DEBUG("server", "Faker: 玩家 {} 的天赋显示已更新", player->GetName());
}

class FakerLogin : public PlayerScript
{
public:
	FakerLogin() : PlayerScript("FakerLogin") {}

	void OnLogin(Player* player) 
	{
		sFaker->OnLogin(player);
	}
};

void AddSC_FakerLogin()
{
	new FakerLogin();
}

class FakerMovement : public PlayerScript
{
public:
    FakerMovement() : PlayerScript("FakerMovement") {}

    void OnPlayerUpdate(Player* player, uint32 p_time) override
    {
        // 只处理机器人的更新事件
        if (!PlayerPatch::GetIsFaker(player))
            return;
            
        // 降低检查频率，使用静态计时器
        static uint32 checkTimer = 0;
        checkTimer += p_time;
        if (checkTimer < 10000) // 每10秒检查一次
            return;
        
        checkTimer = 0;
            
        // ❌ 已禁用：移动控制现在由BotBehaviorEngine统一管理
        // 检查机器人是否在队伍中或被设置为跟随模式
        if (player->GetGroup() || player->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
        {
            // 机器人在队伍中或跟随模式，不执行随机移动
            LOG_DEBUG("server", "FakerMovement: 机器人 {} 在队伍中或跟随模式，跳过随机移动", player->GetName());
            return;
        }

        // 检查机器人是否在移动
        if (!player->HasUnitState(UNIT_STATE_MOVING) &&
            !player->HasUnitState(UNIT_STATE_CASTING) &&
            player->IsAlive() &&
            !player->IsInCombat())
        {
            // 其他移动逻辑由BotBehaviorEngine处理，这里不再强制触发移动
            LOG_DEBUG("server", "FakerMovement: 机器人 {} 移动由BotBehaviorEngine处理", player->GetName());
        }
    }
    
    // 添加登录事件处理
    void OnPlayerLogin(Player* player) override
    {
        if (PlayerPatch::GetIsFaker(player))
        {
            // ❌ 已禁用：登录时的移动现在由BotBehaviorEngine管理
            // 避免与队伍跟随系统冲突
            LOG_DEBUG("server", "FakerMovement: 机器人 {} 登录，移动由BotBehaviorEngine处理", player->GetName());
        }
    }
};

void AddSC_FakerMovement()
{
    new FakerMovement();
}

// 在文件末尾添加数据库表创建脚本
class FakerDatabaseScript : public WorldScript
{
public:
    FakerDatabaseScript() : WorldScript("FakerDatabaseScript") {}

    void OnStartup() override
    {
        LOG_INFO("server", "创建Faker虚拟账号表结构...");
        // 创建虚拟账号表，使用WorldDatabase
        WorldDatabase.Execute("CREATE TABLE IF NOT EXISTS ygbot_accounts ("
                             "original_account INT UNSIGNED NOT NULL,"
                             "virtual_account INT UNSIGNED NOT NULL,"
                             "PRIMARY KEY (original_account),"
                             "UNIQUE KEY (virtual_account)"
                             ")");
        LOG_INFO("server", "Faker虚拟账号表结构创建完成");
    }
};

void AddSC_FakerDatabaseScript()
{
    new FakerDatabaseScript();
}

