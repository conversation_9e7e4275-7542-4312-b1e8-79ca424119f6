﻿    正在创建库 D:/keji/build/src/server/apps/RelWithDebInfo/worldserver.lib 和对象 D:/keji/build/src/server/apps/RelWithDebInfo/worldserver.exp
modules.lib(CombatSystemScript.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotSkillManager.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(YGbotTalentCommands.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(YGbotGlyphCommands.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotEventSystem.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotEventHandlers.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(Faker.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotInteractionOpcodeHandler.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotDuelPlayerScript.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(BotControlCommands.obj) : error LNK2001: 无法解析的外部符号 "class std::unordered_map<unsigned __int64,bool,struct std::hash<unsigned __int64>,struct std::equal_to<unsigned __int64>,class std::allocator<struct std::pair<unsigned __int64 const ,bool> > > PlayerPatch::playerFakerStatus" (?playerFakerStatus@PlayerPatch@@3V?$unordered_map@_K_NU?$hash@_K@std@@U?$equal_to@_K@2@V?$allocator@U?$pair@$$CB_K_N@std@@@2@@std@@A)
modules.lib(CombatSystemScript.obj) : error LNK2019: 无法解析的外部符号 "public: void __cdecl CombatSystemCore::InitializeDatabaseComponents(void)" (?InitializeDatabaseComponents@CombatSystemCore@@QEAAXXZ)，函数 "public: virtual void __cdecl CombatSystemWorldScript::OnStartup(void)" (?OnStartup@CombatSystemWorldScript@@UEAAXXZ) 中引用了该符号
D:\keji\build\bin\RelWithDebInfo\worldserver.exe : fatal error LNK1120: 2 个无法解析的外部命令
