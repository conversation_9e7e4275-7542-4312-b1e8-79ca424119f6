#ifndef BOT_OPCODE_HANDLER_H
#define BOT_OPCODE_HANDLER_H

#include "ScriptMgr.h"
#include "BotEventSystem.h"
#include <functional>

// 前向声明
class Player;
class Unit;
class Creature;
class Quest;
class Item;
class Spell;
class WorldPacket;
class WorldSession;
struct MovementInfo;
struct SpellCastTargets;

// 机器人操作码处理脚本
class BotOpcodeHandlerScript : public PlayerScript
{
public:
    BotOpcodeHandlerScript() : PlayerScript("BotOpcodeHandlerScript") {}

    // 重写玩家更新方法来监听操作码
    void OnPlayerUpdate(Player* player, uint32 diff) override;
    
    // 监听玩家登录
    void OnPlayerLogin(Player* player) override;
    
    // 监听玩家登出
    void OnPlayerLogout(Player* player) override;
    
    // 监听玩家死亡
    void OnPlayerJustDied(Player* player) override;

    // 监听玩家施放法术
    void OnPlayerSpellCast(Player* player, Spell* spell, bool skipCheck) override;

    // 监听玩家击杀
    void OnPlayerKilledByCreature(Creature* killer, Player* killed) override;
    void OnPlayerCreatureKill(Player* killer, Creature* killed) override;
    void OnPlayerPVPKill(Player* killer, Player* killed) override;

private:
    // 检查是否为机器人
    bool IsBotPlayer(Player* player);
};

// 简化的操作码处理器，只保留基本功能

// 注册脚本的函数声明
void AddSC_BotOpcodeHandler();

#endif // BOT_OPCODE_HANDLER_H
