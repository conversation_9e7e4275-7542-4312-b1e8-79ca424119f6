#include "ScriptMgr.h"
#include "Player.h"
#include "WorldSession.h"
#include "PlayerPatch.h"
#include "Log.h"
#include "Util.h"
#include "WorldPacket.h"
#include "Opcodes.h"

// 使用 PlayerScript 的决斗处理脚本
class BotDuelPlayerScript : public PlayerScript
{
public:
    BotDuelPlayerScript() : PlayerScript("BotDuelPlayerScript") 
    {
        LOG_INFO("server", "BotDuelPlayerScript: 决斗玩家脚本已初始化");
    }

    // 当决斗请求时
    void OnPlayerDuelRequest(Player* target, Player* challenger) override
    {
        if (!target || !challenger)
        {
            LOG_WARN("server", "BotDuelPlayerScript: 决斗请求参数无效");
            return;
        }

        LOG_INFO("server", "BotDuelPlayerScript: 玩家 {} 向 {} 发起决斗请求",
            challenger->GetName(), target->GetName());

        LOG_INFO("server", "BotDuelPlayerScript: 检查目标 {} 是否为机器人", target->GetName());

        // 添加更详细的机器人检查
        bool isFaker = PlayerPatch::GetIsFaker(target);
        LOG_INFO("server", "BotDuelPlayerScript: PlayerPatch::GetIsFaker({}) 返回: {}", target->GetName(), isFaker ? "true" : "false");

        // 如果不是机器人，检查是否应该是机器人（根据名字或其他特征）
        if (!isFaker)
        {
            // 检查是否是已知的机器人名字
            std::string playerName = target->GetName();
            if (playerName == "心比天高" || playerName.find("bot") != std::string::npos ||
                playerName.find("机器人") != std::string::npos || playerName.find("rndbot") != std::string::npos)
            {
                LOG_INFO("server", "BotDuelPlayerScript: 根据名字判断 {} 应该是机器人，强制设置", target->GetName());
                PlayerPatch::SetIsFaker(target, true);
                isFaker = true;
            }
            else
            {
                LOG_INFO("server", "BotDuelPlayerScript: {} 不是已知的机器人名字", target->GetName());
            }
        }

        if (isFaker)
        {
            LOG_INFO("server", "BotDuelPlayerScript: 目标 {} 是机器人，开始处理决斗响应", target->GetName());

            // 检查决斗状态
            LOG_INFO("server", "BotDuelPlayerScript: 检查机器人 {} 的决斗状态", target->GetName());
            LOG_INFO("server", "BotDuelPlayerScript: target->duel 存在: {}, target->duel->Initiator 存在: {}",
                target->duel ? "是" : "否", (target->duel && target->duel->Initiator) ? "是" : "否");

            // 直接处理决斗响应
            if (target->duel && target->duel->Initiator)
            {
                LOG_INFO("server", "BotDuelPlayerScript: 机器人 {} 有有效的决斗状态", target->GetName());
                
                // 90% 概率接受决斗
                if (urand(1, 100) <= 90)
                {
                    // 接受决斗
                    WorldPacket acceptPacket(CMSG_DUEL_ACCEPTED, 8);
                    acceptPacket << target->GetGuidValue(PLAYER_DUEL_ARBITER);
                    target->GetSession()->HandleDuelAcceptedOpcode(acceptPacket);
                    LOG_INFO("server", "BotDuelPlayerScript: 机器人 {} 接受了决斗请求", target->GetName());
                }
                else
                {
                    // 拒绝决斗
                    WorldPacket cancelPacket(CMSG_DUEL_CANCELLED, 8);
                    cancelPacket << target->GetGuidValue(PLAYER_DUEL_ARBITER);
                    target->GetSession()->HandleDuelCancelledOpcode(cancelPacket);
                    LOG_INFO("server", "BotDuelPlayerScript: 机器人 {} 拒绝了决斗请求", target->GetName());
                }
            }
            else
            {
                LOG_WARN("server", "BotDuelPlayerScript: 机器人 {} 没有有效的决斗状态", target->GetName());

                // 尝试直接响应，不检查决斗状态
                LOG_INFO("server", "BotDuelPlayerScript: 尝试强制响应决斗请求");

                try
                {
                    // 90% 概率接受决斗
                    if (urand(1, 100) <= 90)
                    {
                        // 接受决斗
                        WorldPacket acceptPacket(CMSG_DUEL_ACCEPTED, 8);
                        acceptPacket << target->GetGuidValue(PLAYER_DUEL_ARBITER);
                        target->GetSession()->HandleDuelAcceptedOpcode(acceptPacket);
                        LOG_INFO("server", "BotDuelPlayerScript: 机器人 {} 强制接受了决斗请求", target->GetName());
                    }
                    else
                    {
                        // 拒绝决斗
                        WorldPacket cancelPacket(CMSG_DUEL_CANCELLED, 8);
                        cancelPacket << target->GetGuidValue(PLAYER_DUEL_ARBITER);
                        target->GetSession()->HandleDuelCancelledOpcode(cancelPacket);
                        LOG_INFO("server", "BotDuelPlayerScript: 机器人 {} 强制拒绝了决斗请求", target->GetName());
                    }
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("server", "BotDuelPlayerScript: 强制响应决斗时发生异常: {}", e.what());
                }
            }
        }
        else
        {
            LOG_INFO("server", "BotDuelPlayerScript: 目标 {} 不是机器人 (PlayerPatch::GetIsFaker: {})",
                target->GetName(), isFaker ? "true" : "false");
        }
    }
};

// 注册脚本
void AddSC_BotDuelPlayerScript()
{
    new BotDuelPlayerScript();
}
