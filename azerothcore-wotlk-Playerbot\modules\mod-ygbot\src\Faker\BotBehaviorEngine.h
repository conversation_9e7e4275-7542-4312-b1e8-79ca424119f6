#ifndef BOT_BEHAVIOR_ENGINE_H
#define BOT_BEHAVIOR_ENGINE_H

#include "BotEventSystem.h"
#include "Player.h"
#include "WorldPacket.h"
#include <random>
#include <chrono>

// 机器人行为引擎 - 用于生成真实的玩家操作码
class BotBehaviorEngine
{
public:
    static BotBehaviorEngine* instance()
    {
        static BotBehaviorEngine instance;
        return &instance;
    }

    // 初始化行为引擎
    void Initialize();
    
    // 更新机器人行为
    void UpdateBot(Player* bot, uint32 diff);
    
    // 模拟真实玩家操作码
    void SimulatePlayerBehavior(Player* bot);
    
    // 发送操作码到机器人会话
    void SendOpcodeToBot(Player* bot, uint16 opcode, WorldPacket& packet);
    
    // 行为模式
    enum BehaviorMode
    {
        BEHAVIOR_IDLE = 0,      // 空闲状态
        BEHAVIOR_EXPLORING,     // 探索模式
        BEHAVIOR_COMBAT,        // 战斗模式
        BEHAVIOR_SOCIAL,        // 社交模式
        BEHAVIOR_QUESTING,      // 任务模式
        BEHAVIOR_TRADING        // 交易模式
    };
    
    // 设置机器人行为模式
    void SetBehaviorMode(Player* bot, BehaviorMode mode);
    BehaviorMode GetBehaviorMode(Player* bot);

private:
    BotBehaviorEngine() = default;
    ~BotBehaviorEngine() = default;
    
    // 行为状态跟踪
    struct BotBehaviorState
    {
        BehaviorMode currentMode = BEHAVIOR_IDLE;
        uint32 lastActionTime = 0;
        uint32 nextActionDelay = 0;
        bool isActive = true;
    };
    
    std::unordered_map<uint64, BotBehaviorState> botStates;
    std::mt19937 randomGenerator;
    
    // 具体行为实现
    void ExecuteIdleBehavior(Player* bot);
    void ExecuteExploringBehavior(Player* bot);
    void ExecuteCombatBehavior(Player* bot);
    void ExecuteSocialBehavior(Player* bot);
    void ExecuteQuestingBehavior(Player* bot);
    void ExecuteTradingBehavior(Player* bot);

    // ✅ 组队移动处理
    void HandleGroupMovement(Player* bot);
    
    // 操作码生成
    void GenerateMovementOpcode(Player* bot);
    void GenerateChatOpcode(Player* bot);
    // void GenerateCombatOpcode(Player* bot); // ❌ 已禁用：战斗操作码现在由BotEventHandlers处理
    void GenerateInteractionOpcode(Player* bot);
    
    // 辅助方法
    uint32 GetRandomDelay(uint32 min, uint32 max);
    bool ShouldPerformAction(Player* bot, uint32 probability);
    void UpdateBehaviorState(Player* bot);
};

// 全局访问宏
#define sBotBehaviorEngine BotBehaviorEngine::instance()

// 增强的操作码模拟器
class BotOpcodeSimulator
{
public:
    static BotOpcodeSimulator* instance()
    {
        static BotOpcodeSimulator instance;
        return &instance;
    }
    
    // 模拟移动操作码
    void SimulateMovement(Player* bot, float x, float y, float z, float orientation);
    void SimulateMovementStart(Player* bot, uint32 moveType);
    void SimulateMovementStop(Player* bot);
    
    // 模拟聊天操作码
    void SimulateChat(Player* bot, const std::string& message, uint32 chatType);
    void SimulateEmote(Player* bot, uint32 emoteId);
    
    // 模拟战斗操作码
    void SimulateAttack(Player* bot, Unit* target);
    void SimulateSpellCast(Player* bot, uint32 spellId, Unit* target = nullptr);
    void SimulateCombatMovement(Player* bot, Unit* target);
    
    // 模拟交互操作码
    void SimulateNPCInteraction(Player* bot, Creature* npc);
    void SimulateObjectInteraction(Player* bot, GameObject* obj);
    void SimulateQuestInteraction(Player* bot, uint32 questId, bool accept);
    
    // 模拟物品操作码
    void SimulateItemUse(Player* bot, uint32 itemId);
    void SimulateItemEquip(Player* bot, uint32 itemId);
    void SimulateLootAction(Player* bot);
    
    // 模拟社交操作码
    void SimulateGroupInvite(Player* bot, Player* target);
    void SimulateGroupResponse(Player* bot, bool accept);
    void SimulateTradeRequest(Player* bot, Player* target);

private:
    BotOpcodeSimulator() = default;
    ~BotOpcodeSimulator() = default;
    
    // 创建并发送操作码包
    void CreateAndSendPacket(Player* bot, uint16 opcode, std::function<void(WorldPacket&)> packetBuilder);
    
    // 操作码常量 (使用实际的AzerothCore操作码值)
    static const uint16 CMSG_MOVE_START_FORWARD = 0x00B1;
    static const uint16 CMSG_MOVE_START_BACKWARD = 0x00B2;
    static const uint16 CMSG_MOVE_STOP = 0x00B5;
    static const uint16 CMSG_MESSAGECHAT = 0x0095;
    static const uint16 CMSG_TEXT_EMOTE = 0x0096;
    static const uint16 CMSG_ATTACKSWING = 0x0141;
    static const uint16 CMSG_CAST_SPELL = 0x012E;
    static const uint16 CMSG_GOSSIP_HELLO = 0x017B;
    static const uint16 CMSG_USE_ITEM = 0x00AB;
};

// 全局访问宏
#define sBotOpcodeSimulator BotOpcodeSimulator::instance()

// 智能行为调度器
class BotBehaviorScheduler
{
public:
    static BotBehaviorScheduler* instance()
    {
        static BotBehaviorScheduler instance;
        return &instance;
    }
    
    // 调度机器人行为
    void ScheduleBehavior(Player* bot, BotBehaviorEngine::BehaviorMode mode, uint32 duration);
    
    // 更新调度器
    void Update(uint32 diff);
    
    // 添加定时任务
    void AddScheduledTask(Player* bot, std::function<void()> task, uint32 delay);

private:
    struct ScheduledBehavior
    {
        Player* bot;
        BotBehaviorEngine::BehaviorMode mode;
        uint32 endTime;
    };
    
    struct ScheduledTask
    {
        Player* bot;
        std::function<void()> task;
        uint32 executeTime;
    };
    
    std::vector<ScheduledBehavior> scheduledBehaviors;
    std::vector<ScheduledTask> scheduledTasks;
    
    BotBehaviorScheduler() = default;
    ~BotBehaviorScheduler() = default;
};

// 全局访问宏
#define sBotBehaviorScheduler BotBehaviorScheduler::instance()

#endif // BOT_BEHAVIOR_ENGINE_H
