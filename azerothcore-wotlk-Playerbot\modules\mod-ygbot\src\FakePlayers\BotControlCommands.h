#ifndef BOT_CONTROL_COMMANDS_H
#define BOT_CONTROL_COMMANDS_H

#include "Player.h"
#include "Chat.h"
#include "ScriptMgr.h"
#include "Group.h"
#include "Guild.h"
#include "SocialMgr.h"
#include "WorldPacket.h"
#include "Opcodes.h"
#include "Channel.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <functional>

// 前向声明
class Player;
class WorldSession;
class Group;
class Guild;

// 机器人控制命令处理器
class BotControlCommands
{
public:
    static BotControlCommands* instance()
    {
        static BotControlCommands instance;
        return &instance;
    }

    // 初始化命令系统
    void Initialize();

    // 机器人管理命令处理
    bool HandleOnlineGuildMemberCommand(Player* player);
    bool HandleOfflineAllBotCommand(Player* player);
    bool HandleOnlineFriendsCommand(Player* player);
    bool HandleGroupFriendCommand(Player* player);
    bool HandleInviteFriendCommand(Player* player);
    bool HandleAddClassBotCommand(Player* player, uint8 classId);
    bool HandleResetDungeonCommand(Player* player);

    // 聊天频道控制命令处理（仅支持队伍聊天和私聊）
    bool HandleChatCommand(Player* sender, const std::string& message, uint32 chatType);

    // 机器人私聊命令处理
    bool HandleWhisperCommand(Player* bot, Player* sender, const std::string& message);

    // GM命令处理（支持说话频道）
    bool HandleGMCommand(Player* sender, const std::string& message);

private:
    // 战斗控制命令
    void HandleAttackCommand(Player* sender, const std::string& targetType = "");
    void HandleStopAttackCommand(Player* sender, const std::string& targetType = "");
    void HandleCombatAIStatusCommand(Player* sender); // 检查CombatAI状态
    void HandleSkillTestCommand(Player* sender); // 技能测试命令
    void HandleForceSpellCommand(Player* sender); // 强制施法测试命令
    void HandleCombatTestCommand(Player* sender); // 战斗测试命令
    void HandleClassSupportCommand(Player* sender); // 职业支持检查命令

    // 位置与阵型命令
    void HandleGatherCommand(Player* sender);
    void HandleSummonAllCommand(Player* sender);
    void HandleSpreadCommand(Player* sender);
    void HandleDispelCommand(Player* sender);
    void HandleTriangleFormationCommand(Player* sender);
    void HandleCircleFormationCommand(Player* sender);
    void HandleAvoidCrowdCommand(Player* sender);

    // 私聊命令处理
    void HandleLeaveGroupCommand(Player* bot);
    void HandleLeaveArenaTeamCommand(Player* bot, uint8 teamType);
    void HandleLeaveArenaTeamByName(Player* bot, const std::string& teamName);
    void HandleLeaveArenaTeamByIndex(Player* bot, uint8 teamIndex);
    void HandleLeaveGuildCommand(Player* bot);
    void HandleAvoidCrowdWhisperCommand(Player* bot, Player* sender);

    // 辅助函数
    std::vector<Player*> GetNearbyBots(Player* player, float range = 100.0f);
    std::vector<Player*> GetGroupBots(Player* player);
    std::vector<Player*> GetGuildBots(Player* player);
    std::string GetClassNameById(uint8 classId);
    std::string GetRaceNameById(uint8 raceId);

public:
    bool IsPluginMessage(const std::string& message);
    std::vector<Player*> GetFriendBots(Player* player);
    std::vector<Player*> GetBotsByClass(uint8 classId, uint32 maxCount = 10);
    
    bool IsMeleeClass(uint8 classId);
    bool IsRangedClass(uint8 classId);
    bool IsTankClass(uint8 classId);
    
    void SendOpcodeToBot(Player* bot, uint16 opcode, WorldPacket& packet);
    void MakeBotAttackTarget(Player* bot, Unit* target);
    void MakeBotStopAttack(Player* bot);
    void MakeBotMoveTo(Player* bot, float x, float y, float z);
    void MakeBotTeleportTo(Player* bot, float x, float y, float z);
    bool MakeBotTeleportTo(Player* bot, float x, float y, float z, uint32 mapId); // 增强版：支持跨地图传送
    void MakeBotFollowPlayer(Player* bot, Player* target, float distance = 5.0f);
    void SetBotGroupFollowMode(Player* bot, bool enable);

    // 旧的战斗系统相关代码已被删除 - 现在使用新的高级战斗系统
    void UpdateCombatMovement(Player* bot, Unit* target); // 保留移动函数，可能被新系统使用

public:
    void UpdateBotGroupFollow();
    // UpdateActiveCombats已被删除 - 旧战斗系统已被新的高级战斗系统替代

    // 命令映射
    std::unordered_map<std::string, std::function<void(Player*)>> _chatCommands;
    std::unordered_map<std::string, std::function<void(Player*, Player*)>> _whisperCommands;
    
    // 初始化命令映射
    void InitializeChatCommands();
    void InitializeWhisperCommands();
};

#define sBotControlCommands BotControlCommands::instance()

// 聊天命令脚本 - 使用正确的钩子注册
class BotControlChatScript : public PlayerScript
{
public:
    BotControlChatScript() : PlayerScript("BotControlChatScript",
    {
        PLAYERHOOK_ON_CHAT,
        PLAYERHOOK_ON_CHAT_WITH_RECEIVER,
        PLAYERHOOK_ON_CHAT_WITH_GROUP,
        PLAYERHOOK_ON_CHAT_WITH_GUILD,
        PLAYERHOOK_ON_CHAT_WITH_CHANNEL
    }) {}

    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Player* receiver) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Group* group) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Guild* guild) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Channel* channel) override;
};

// 聊天操作码处理器
class BotControlChatOpcodeScript : public PlayerScript
{
public:
    BotControlChatOpcodeScript() : PlayerScript("BotControlChatOpcodeScript") {}

    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Player* receiver) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Group* group) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Guild* guild) override;
    void OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Channel* channel) override;
};

// GM命令脚本
class BotControlCommandScript : public CommandScript
{
public:
    BotControlCommandScript() : CommandScript("BotControlCommandScript") {}

    Acore::ChatCommands::ChatCommandTable GetCommands() const override;

private:
    static bool HandleOnlineGuildMemberCommand(ChatHandler* handler, const char* args);
    static bool HandleOfflineAllBotCommand(ChatHandler* handler, const char* args);
    static bool HandleOnlineFriendsCommand(ChatHandler* handler, const char* args);
    static bool HandleGroupFriendCommand(ChatHandler* handler, const char* args);
    static bool HandleInviteFriendCommand(ChatHandler* handler, const char* args);
    static bool HandleAddClassBotCommand(ChatHandler* handler, const char* args);
    static bool HandleResetDungeonCommand(ChatHandler* handler, const char* args);
    static bool HandleTestCommand(ChatHandler* handler, const char* args);
};

#endif // BOT_CONTROL_COMMANDS_H
