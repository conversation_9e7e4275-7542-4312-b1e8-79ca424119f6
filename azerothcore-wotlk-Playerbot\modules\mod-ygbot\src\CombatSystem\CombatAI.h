#ifndef _COMBAT_AI_H_
#define _COMBAT_AI_H_

#include "Define.h"
#include "Player.h"
#include "Unit.h"
#include "Spell.h"
#include <vector>
#include <unordered_map>
#include <memory>

// 包含枚举定义
#include "CombatSystemCore.h"
#include "BotSkillManager.h"

// 战斗AI基类
class AC_GAME_API CombatAI
{
public:
    explicit CombatAI(Player* player);
    virtual ~CombatAI() = default;
    
    // 核心更新函数
    virtual void Update(uint32 diff);
    
    // 战斗状态管理
    virtual void EnterCombat(Unit* target);
    virtual void LeaveCombat();
    virtual void OnDamageReceived(Unit* attacker, uint32 damage);
    virtual void OnDamageDealt(Unit* target, uint32 damage);
    virtual void OnSpellCast(uint32 spellId, Unit* target);
    
    // 目标管理
    virtual Unit* SelectTarget();
    virtual void UpdateTargetList();
    virtual bool IsValidTarget(Unit* target);
    virtual float CalculateTargetPriority(Unit* target);
    
    // 法术管理
    virtual bool CastSpell(uint32 spellId, Unit* target = nullptr);
    virtual bool CanCastSpell(uint32 spellId, Unit* target = nullptr);
    virtual uint32 SelectBestSpell(SpellType type);
    virtual void UpdateSpellCooldowns(uint32 diff);
    
    // 移动和定位
    virtual void UpdateMovement();
    virtual void MoveToTarget(Unit* target);
    virtual void MoveToPosition(float x, float y, float z);
    virtual void Flee();
    virtual bool IsInRange(Unit* target, float range);
    
    // 状态检查
    virtual bool ShouldFlee();
    virtual bool ShouldHeal();
    virtual bool ShouldBuff();
    virtual bool NeedsMana();
    
    // 获取器
    Player* GetPlayer() const { return _player; }
    CombatState GetCombatState() const { return _combatState; }
    Unit* GetCurrentTarget() const { return _currentTarget; }
    CombatScenarioType GetScenarioType() const { return _scenarioType; }

    // 技能管理公开接口
    void RefreshLearnedSpells() { LoadLearnedSpells(); }
    
protected:
    // 核心数据
    Player* _player;
    CombatState _combatState;
    CombatScenarioType _scenarioType;
    Unit* _currentTarget;
    
    // 目标列表
    std::vector<CombatTarget> _targetList;
    std::vector<CombatTarget> _friendlyList;
    
    // 法术数据
    std::unordered_map<uint32, SpellInfo_Combat> _spellBook;
    std::unordered_map<uint32, uint32> _spellCooldowns;

    // 技能列表 - 从玩家已学技能中构建
    std::vector<uint32> _damageSpells;
    std::vector<uint32> _healSpells;
    std::vector<uint32> _buffSpells;
    std::vector<uint32> _debuffSpells;
    std::vector<uint32> _controlSpells;
    std::vector<uint32> _defensiveSpells;
    
    // 计时器
    uint32 _targetUpdateTimer;
    uint32 _movementUpdateTimer;
    uint32 _spellUpdateTimer;
    uint32 _combatTimer;
    
    // 战斗参数
    float _combatRange;
    float _fleeRange;
    uint32 _reactionTime;
    
    // 辅助函数
    virtual void InitializeSpells();
    virtual void LoadClassSpells();
    virtual void LoadLearnedSpells();  // 从玩家已学技能中构建技能列表
    void ClassifySpell(uint32 spellId, const SpellInfo* spellInfo);  // 技能分类
    virtual void UpdateCombatState();
    virtual void ProcessCombatLogic();
    virtual void HandleEmergency();
    
    // 法术辅助函数
    bool HasSpell(uint32 spellId);
    bool IsSpellReady(uint32 spellId);
    uint32 GetSpellCooldown(uint32 spellId);
    void SetSpellCooldown(uint32 spellId, uint32 cooldown);
    
    // 目标辅助函数
    void AddTarget(Unit* target);
    void RemoveTarget(Unit* target);
    void ClearTargets();
    Unit* FindNearestEnemy();
    Unit* FindWeakestEnemy();
    Unit* FindStrongestEnemy();
    
    // 移动辅助函数
    bool IsInMeleeRange(Unit* target);
    bool IsInSpellRange(Unit* target, uint32 spellId);
    float GetOptimalRange(Unit* target);
    void StopMovement();
    
    // 资源管理
    bool HasEnoughMana(uint32 spellId);
    bool HasEnoughHealth(uint32 percentage);
    uint32 GetHealthPercentage();
    uint32 GetManaPercentage();
    
    // 调试和日志
    void LogCombatAction(const std::string& action, Unit* target = nullptr);
    void LogSpellCast(uint32 spellId, Unit* target = nullptr);
    void LogMovement(const std::string& action);
};

// 职业特化AI基类
class AC_GAME_API ClassCombatAI : public CombatAI
{
public:
    explicit ClassCombatAI(Player* player) : CombatAI(player) {}
    virtual ~ClassCombatAI() = default;
    
    // 职业特定函数
    virtual void UseClassAbilities();
    virtual void HandleClassSpecificSituation();
    virtual bool CanUseClassFeature(const std::string& feature);
    
protected:
    // 职业特定法术ID
    std::unordered_map<std::string, uint32> _classSpells;
    
    // 职业特定逻辑
    virtual void InitializeClassSpells();
    virtual void UpdateClassResources();
    virtual void HandleClassCombos();
};

// 战斗AI工厂
class AC_GAME_API CombatAIFactory
{
public:
    static std::unique_ptr<CombatAI> CreateCombatAI(Player* player, CombatScenarioType scenario);
    static std::unique_ptr<ClassCombatAI> CreateClassCombatAI(Player* player, CombatScenarioType scenario);

private:
    static std::unique_ptr<CombatAI> CreateFieldAI(Player* player);
    static std::unique_ptr<CombatAI> CreateBattlegroundAI(Player* player);
    static std::unique_ptr<CombatAI> CreateArenaAI(Player* player);
    static std::unique_ptr<CombatAI> CreateDuelAI(Player* player);
};

#endif // _COMBAT_AI_H_
