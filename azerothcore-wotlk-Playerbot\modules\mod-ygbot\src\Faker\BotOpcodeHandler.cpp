#include "BotOpcodeHandler.h"
#include "PlayerPatch.h"
#include "BotEventSystem.h"
#include "Log.h"
#include "Player.h"
#include "Unit.h"
#include "Creature.h"
#include "WorldPacket.h"
#include "Opcodes.h"

// BotOpcodeHandlerScript 实现

void BotOpcodeHandlerScript::OnPlayerUpdate(Player* player, uint32 diff)
{
    if (!IsBotPlayer(player))
        return;
    
    // 定期触发更新事件
    static uint32 updateTimer = 0;
    updateTimer += diff;
    
    if (updateTimer >= 10000) // 每10秒触发一次
    {
        updateTimer = 0;
        sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_UPDATE, player);
    }
}

void BotOpcodeHandlerScript::OnPlayerLogin(Player* player)
{
    if (!IsBotPlayer(player))
        return;
    
    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} 登录", player->GetName());
    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_LOGIN, player);
}

void BotOpcodeHandlerScript::OnPlayerLogout(Player* player)
{
    if (!IsBotPlayer(player))
        return;
    
    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} 登出", player->GetName());
    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_LOGOUT, player);
}

// 简化的实现，移除了不存在的方法

void BotOpcodeHandlerScript::OnPlayerJustDied(Player* player)
{
    if (!IsBotPlayer(player))
        return;

    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} 死亡", player->GetName());
    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_DEATH, player);
}

void BotOpcodeHandlerScript::OnPlayerSpellCast(Player* player, Spell* spell, bool skipCheck)
{
    if (!IsBotPlayer(player))
        return;

    LOG_TRACE("server", "BotOpcodeHandler: 机器人 {} 施放法术", player->GetName());
    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_SPELL_CAST, player);
}

void BotOpcodeHandlerScript::OnPlayerKilledByCreature(Creature* killer, Player* killed)
{
    if (!IsBotPlayer(killed))
        return;
    
    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} 被生物击杀", killed->GetName());
    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_DEATH, killed, killer);
}

void BotOpcodeHandlerScript::OnPlayerCreatureKill(Player* killer, Creature* killed)
{
    if (!IsBotPlayer(killer))
        return;
    
    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} 击杀生物", killer->GetName());
    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_KILL, killer, killed);
}

void BotOpcodeHandlerScript::OnPlayerPVPKill(Player* killer, Player* killed)
{
    if (!IsBotPlayer(killer))
        return;
    
    LOG_DEBUG("server", "BotOpcodeHandler: 机器人 {} PVP击杀玩家", killer->GetName());
    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_KILL, killer, killed);
}

bool BotOpcodeHandlerScript::IsBotPlayer(Player* player)
{
    return player && PlayerPatch::GetIsFaker(player);
}

// 注册脚本
void AddSC_BotOpcodeHandler()
{
    new BotOpcodeHandlerScript();
}
