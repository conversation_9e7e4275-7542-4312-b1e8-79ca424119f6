#include "BotEventHandlers.h"
#include "PlayerPatch.h"
#include "Log.h"
#include "Player.h"
#include "WorldSession.h"
#include "WorldPacket.h"
#include "Opcodes.h"
#include "PetitionMgr.h"
#include "Util.h"
#include "DatabaseEnv.h"
#include "ObjectAccessor.h"

// 这个文件包含InteractionResponseEventHandler的剩余实现

// 处理决斗请求
void InteractionResponseEventHandler::HandleDuelRequest(const BotInteractionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到决斗请求", bot->GetName());
    
    // 决定是否接受邀请
    bool shouldAccept = ShouldRespond(RESPONSE_DUEL_REQUEST, bot);
    uint32 delay = urand(1000, 4000);
    
    if (shouldAccept)
    {
        DelayedResponse(bot, RESPONSE_DUEL_REQUEST, [bot]() {
            WorldSession* session = bot->GetSession();
            if (session && bot->duel && bot->duel->Initiator)
            {
                // 直接调用决斗接受处理函数，需要包含仲裁者GUID
                WorldPacket acceptPacket(CMSG_DUEL_ACCEPTED, 8);
                acceptPacket << bot->GetGuidValue(PLAYER_DUEL_ARBITER);
                session->HandleDuelAcceptedOpcode(acceptPacket);
                LOG_INFO("server", "机器人 {} 接受了决斗请求", bot->GetName());
            }
            else
            {
                LOG_WARN("server", "机器人 {} 没有有效的决斗请求", bot->GetName());
            }
        }, delay);
    }
    else
    {
        DelayedResponse(bot, RESPONSE_DUEL_REQUEST, [bot]() {
            WorldSession* session = bot->GetSession();
            if (session && bot->duel && bot->duel->Initiator)
            {
                // 直接调用决斗取消处理函数，需要包含仲裁者GUID
                WorldPacket cancelPacket(CMSG_DUEL_CANCELLED, 8);
                cancelPacket << bot->GetGuidValue(PLAYER_DUEL_ARBITER);
                session->HandleDuelCancelledOpcode(cancelPacket);
                LOG_INFO("server", "机器人 {} 拒绝了决斗请求", bot->GetName());
            }
            else
            {
                LOG_WARN("server", "机器人 {} 没有有效的决斗请求可拒绝", bot->GetName());
            }
        }, delay);
    }
}

// 处理交易请求
void InteractionResponseEventHandler::HandleTradeRequest(const BotTradeEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到交易请求", bot->GetName());
    
    // 始终接受交易请求
    uint32 delay = urand(1000, 3000);
    
    DelayedResponse(bot, RESPONSE_TRADE_REQUEST, [bot]() {
        WorldSession* session = bot->GetSession();
        if (session && bot->GetTradeData())
        {
            // 直接调用交易开始处理函数
            WorldPacket emptyPacket;
            session->HandleBeginTradeOpcode(emptyPacket);
            LOG_INFO("server", "机器人 {} 接受了交易请求", bot->GetName());
        }
    }, delay);
}

// 处理交易开始
void InteractionResponseEventHandler::HandleTradeBegin(const BotTradeEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 开始交易", bot->GetName());
    
    // 交易窗口已经打开，等待玩家操作
    // 这里可以添加一些物品到交易窗口的逻辑
}

// 处理交易确认
void InteractionResponseEventHandler::HandleTradeAccept(const BotTradeEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到交易确认", bot->GetName());
    
    // 玩家已经确认交易，机器人也确认
    uint32 delay = urand(500, 1500);
    
    DelayedResponse(bot, RESPONSE_TRADE_ACCEPT, [bot]() {
        WorldSession* session = bot->GetSession();
        if (session && bot->GetTradeData())
        {
            // 直接调用交易确认处理函数
            WorldPacket emptyPacket;
            session->HandleAcceptTradeOpcode(emptyPacket);
            LOG_INFO("server", "机器人 {} 确认了交易", bot->GetName());
        }
    }, delay);
}

// 处理公会章程签名
void InteractionResponseEventHandler::HandleGuildCharter(const BotPetitionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到公会章程签名请求", bot->GetName());
    
    // 决定是否签名
    bool shouldSign = ShouldRespond(RESPONSE_GUILD_CHARTER, bot);
    uint32 delay = urand(2000, 5000);
    
    if (shouldSign)
    {
        DelayedResponse(bot, RESPONSE_GUILD_CHARTER, [bot, eventData]() {
            WorldSession* session = bot->GetSession();
            if (session && !eventData.petitionGuid.IsEmpty())
            {
                try
                {
                    // 1. 直接添加签名到PetitionMgr
                    sPetitionMgr->AddSignature(eventData.petitionGuid, session->GetAccountId(), bot->GetGUID());

                    // 2. 手动添加到数据库
                    CharacterDatabasePreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_PETITION_SIGNATURE);
                    stmt->SetData(0, eventData.inviter ? eventData.inviter->GetGUID().GetCounter() : 0);
                    stmt->SetData(1, eventData.petitionGuid.GetCounter());
                    stmt->SetData(2, bot->GetGUID().GetCounter());
                    stmt->SetData(3, session->GetAccountId());
                    CharacterDatabase.Execute(stmt);

                    // 3. 通知客户端更新
                    WorldPacket resultData(SMSG_PETITION_SIGN_RESULTS, 20);
                    resultData << eventData.petitionGuid;
                    resultData << bot->GetGUID();
                    resultData << uint32(0); // PETITION_SIGN_OK

                    // 发送给机器人
                    session->SendPacket(&resultData);

                    // 发送给章程所有者
                    if (eventData.inviter && eventData.inviter->IsInWorld())
                        eventData.inviter->GetSession()->SendPacket(&resultData);

                    LOG_INFO("server", "机器人 {} 签名了公会章程并更新了数据库", bot->GetName());
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("server", "机器人 {} 签名公会章程时发生异常: {}", bot->GetName(), e.what());
                }
            }
        }, delay);
    }
    else
    {
        LOG_INFO("server", "机器人 {} 拒绝签名公会章程", bot->GetName());
    }
}

// 处理竞技场章程签名
void InteractionResponseEventHandler::HandleArenaCharter(const BotPetitionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到竞技场章程签名请求", bot->GetName());
    
    // 决定是否签名
    bool shouldSign = ShouldRespond(RESPONSE_ARENA_CHARTER, bot);
    uint32 delay = urand(2000, 5000);
    
    if (shouldSign)
    {
        DelayedResponse(bot, RESPONSE_ARENA_CHARTER, [bot, eventData]() {
            WorldSession* session = bot->GetSession();
            if (session && !eventData.petitionGuid.IsEmpty())
            {
                try
                {
                    // 1. 直接添加签名到PetitionMgr
                    sPetitionMgr->AddSignature(eventData.petitionGuid, session->GetAccountId(), bot->GetGUID());

                    // 2. 手动添加到数据库
                    CharacterDatabasePreparedStatement* stmt = CharacterDatabase.GetPreparedStatement(CHAR_INS_PETITION_SIGNATURE);
                    stmt->SetData(0, eventData.inviter ? eventData.inviter->GetGUID().GetCounter() : 0);
                    stmt->SetData(1, eventData.petitionGuid.GetCounter());
                    stmt->SetData(2, bot->GetGUID().GetCounter());
                    stmt->SetData(3, session->GetAccountId());
                    CharacterDatabase.Execute(stmt);

                    // 3. 通知客户端更新
                    WorldPacket resultData(SMSG_PETITION_SIGN_RESULTS, 20);
                    resultData << eventData.petitionGuid;
                    resultData << bot->GetGUID();
                    resultData << uint32(0); // PETITION_SIGN_OK

                    // 发送给机器人
                    session->SendPacket(&resultData);

                    // 发送给章程所有者
                    if (eventData.inviter && eventData.inviter->IsInWorld())
                        eventData.inviter->GetSession()->SendPacket(&resultData);

                    LOG_INFO("server", "机器人 {} 签名了竞技场章程并更新了数据库", bot->GetName());
                }
                catch (const std::exception& e)
                {
                    LOG_ERROR("server", "机器人 {} 签名竞技场章程时发生异常: {}", bot->GetName(), e.what());
                }
            }
        }, delay);
    }
    else
    {
        LOG_INFO("server", "机器人 {} 拒绝签名竞技场章程", bot->GetName());
    }
}

// 处理战场邀请
void InteractionResponseEventHandler::HandleBattlefieldInvite(const BotInteractionEventData& eventData)
{
    Player* bot = eventData.bot;
    if (!bot || !bot->IsInWorld())
        return;
        
    LOG_INFO("server", "InteractionResponseEventHandler: 机器人 {} 收到战场邀请", bot->GetName());
    
    // 决定是否接受邀请
    bool shouldAccept = ShouldRespond(RESPONSE_BATTLEFIELD_INVITE, bot);
    uint32 delay = urand(1000, 4000);
    
    if (shouldAccept)
    {
        DelayedResponse(bot, RESPONSE_BATTLEFIELD_INVITE, [bot, eventData]() {
            WorldSession* session = bot->GetSession();
            if (session)
            {
                // 创建战场邀请接受数据包
                WorldPacket* acceptPacket = new WorldPacket(CMSG_BATTLEFIELD_MGR_ENTRY_INVITE_RESPONSE, 5);
                *acceptPacket << uint32(eventData.targetId); // 战场ID
                *acceptPacket << uint8(1); // 接受
                session->QueuePacket(acceptPacket);
                LOG_INFO("server", "机器人 {} 接受了战场邀请", bot->GetName());
            }
        }, delay);
    }
    else
    {
        DelayedResponse(bot, RESPONSE_BATTLEFIELD_INVITE, [bot, eventData]() {
            WorldSession* session = bot->GetSession();
            if (session)
            {
                // 创建战场邀请拒绝数据包
                WorldPacket* declinePacket = new WorldPacket(CMSG_BATTLEFIELD_MGR_ENTRY_INVITE_RESPONSE, 5);
                *declinePacket << uint32(eventData.targetId); // 战场ID
                *declinePacket << uint8(0); // 拒绝
                session->QueuePacket(declinePacket);
                LOG_INFO("server", "机器人 {} 拒绝了战场邀请", bot->GetName());
            }
        }, delay);
    }
}
