#include "BotTerrainValidator.h"
#include "BotMovementConstants.h"
#include "../PlayerPatch.h"
#include "ObjectAccessor.h"
#include "MapMgr.h"
#include <cmath>
#include <algorithm>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// PathPoint 实现
float PathPoint::DistanceTo(const PathPoint& other) const
{
    float dx = x - other.x;
    float dy = y - other.y;
    float dz = z - other.z;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

// BotTerrainValidator 实现
void BotTerrainValidator::Initialize()
{
    if (isInitialized)
        return;

    LOG_INFO("server.loading", "BotTerrainValidator: 初始化机器人地形验证器...");

    // 初始化日志级别
    logLevel = LOG_INFO;

    // 重置统计信息
    ResetStats();

    isInitialized = true;
    LOG_INFO("server.loading", "BotTerrainValidator: 地形验证器初始化完成");
}

TerrainValidationResult BotTerrainValidator::ValidatePosition(Player* bot, float x, float y, float z)
{
    TerrainValidationResult result;
    
    if (!bot || !bot->GetMap())
    {
        result.errorMessage = "无效的机器人或地图";
        return result;
    }
    
    // 检查基本坐标范围
    if (x < -20000.0f || x > 20000.0f || y < -20000.0f || y > 20000.0f)
    {
        result.errorMessage = "坐标超出有效范围";
        return result;
    }
    
    // 获取地面高度
    result.groundHeight = bot->GetMap()->GetHeight(x, y, BOT_MOVEMENT_MAX_HEIGHT);
    if (result.groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
    {
        result.errorMessage = "Cannot get ground height";
        return result;
    }
    
    // 获取水位高度
    result.waterLevel = GetWaterLevel(bot, x, y);
    
    // 确定地形类型
    result.terrainType = GetTerrainType(bot, x, y, z);
    
    // 检查各种移动能力
    result.canWalk = IsWalkable(bot, x, y, z);
    result.canSwim = IsInWater(bot, x, y, z);
    result.canFly = (z > result.groundHeight + BOT_MOVEMENT_MAX_STEP_HEIGHT);
    
    // 验证位置是否有效
    switch (result.terrainType)
    {
        case TERRAIN_GROUND:
            result.isValid = result.canWalk;
            break;
        case TERRAIN_WATER:
            result.isValid = result.canSwim || result.canWalk;
            break;
        case TERRAIN_AIR:
            // 严格限制飞行高度，防止飞天
            if (z > result.groundHeight + BOT_MOVEMENT_FLY_HEIGHT_MAX)
            {
                result.isValid = false;
                result.errorMessage = "飞行高度过高";
            }
            else
            {
                result.isValid = result.canFly;
            }
            break;
        case TERRAIN_UNDERGROUND:
            // 严格禁止地下移动，防止遁地
            result.isValid = false;
            result.errorMessage = "禁止地下移动";
            break;
        case TERRAIN_INVALID:
        default:
            result.isValid = false;
            result.errorMessage = "无效的地形类型";
            break;
    }
    
    return result;
}

PathValidationResult BotTerrainValidator::ValidatePath(Player* bot, float startX, float startY, float startZ,
                                                      float endX, float endY, float endZ, 
                                                      bool allowFly, bool allowSwim)
{
    PathValidationResult result;
    
    if (!bot || !bot->GetMap())
    {
        result.errorMessage = "无效的机器人或地图";
        return result;
    }
    
    // 计算总距离
    float dx = endX - startX;
    float dy = endY - startY;
    float dz = endZ - startZ;
    result.totalDistance = std::sqrt(dx * dx + dy * dy + dz * dz);
    
    // 如果距离太短，直接返回有效
    if (result.totalDistance < 1.0f)
    {
        result.isValid = true;
        result.validPath.push_back(PathPoint(startX, startY, startZ));
        result.validPath.push_back(PathPoint(endX, endY, endZ));
        return result;
    }
    
    // 检查路径上的多个点
    std::vector<PathPoint> pathPoints;
    for (int i = 0; i <= BOT_MOVEMENT_PATH_CHECK_POINTS; ++i)
    {
        float ratio = static_cast<float>(i) / BOT_MOVEMENT_PATH_CHECK_POINTS;
        float checkX = startX + dx * ratio;
        float checkY = startY + dy * ratio;
        float checkZ = startZ + dz * ratio;
        
        PathPoint point(checkX, checkY, checkZ);
        
        // 验证这个点
        TerrainValidationResult validation = ValidatePosition(bot, checkX, checkY, checkZ);
        point.terrainType = validation.terrainType;
        point.isWalkable = validation.canWalk;
        
        // 检查是否可以通过
        bool canPass = false;
        if (validation.canWalk)
            canPass = true;
        else if (allowSwim && validation.canSwim)
            canPass = true;
        else if (allowFly && validation.canFly)
            canPass = true;
        
        if (canPass)
        {
            result.validPath.push_back(point);
        }
        else
        {
            result.invalidPoints.push_back(point);
        }
        
        pathPoints.push_back(point);
    }
    
    // 检查坡度
    for (size_t i = 1; i < pathPoints.size(); ++i)
    {
        const PathPoint& prev = pathPoints[i - 1];
        const PathPoint& curr = pathPoints[i];
        
        if (!IsSlopeWalkable(bot, prev.x, prev.y, prev.z, curr.x, curr.y, curr.z))
        {
            result.invalidPoints.push_back(curr);
        }
    }
    
    // 如果无效点太多，路径无效
    float invalidRatio = static_cast<float>(result.invalidPoints.size()) / pathPoints.size();
    result.isValid = (invalidRatio < 0.3f); // 允许30%的无效点
    
    if (!result.isValid)
    {
        result.errorMessage = "路径包含太多无效点";
    }
    
    return result;
}

float BotTerrainValidator::GetSafeGroundHeight(Player* bot, float x, float y)
{
    if (!bot || !bot->GetMap())
        return BOT_MOVEMENT_INVALID_HEIGHT;

    float groundHeight = bot->GetMap()->GetHeight(x, y, BOT_MOVEMENT_MAX_HEIGHT);
    if (groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
        return BOT_MOVEMENT_INVALID_HEIGHT;
    
    // 稍微抬高一点，避免卡在地面
    return groundHeight + 0.5f;
}

bool BotTerrainValidator::IsInWater(Player* bot, float x, float y, float z)
{
    if (!bot || !bot->GetMap())
        return false;
    
    float waterLevel = GetWaterLevel(bot, x, y);
    if (waterLevel == BOT_MOVEMENT_INVALID_HEIGHT)
        return false;

    return z <= waterLevel + BOT_MOVEMENT_WATER_TOLERANCE;
}

bool BotTerrainValidator::IsWalkable(Player* bot, float x, float y, float z)
{
    if (!bot || !bot->GetMap())
        return false;
    
    float groundHeight = bot->GetMap()->GetHeight(x, y, BOT_MOVEMENT_MAX_HEIGHT);
    if (groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
        return false;

    // 检查是否在地面附近
    float heightDiff = std::abs(z - groundHeight);
    if (heightDiff > BOT_MOVEMENT_GROUND_TOLERANCE)
        return false;
    
    // 检查是否在建筑物内
    if (IsInsideBuilding(bot, x, y, z))
        return false;
    
    return true;
}

bool BotTerrainValidator::CorrectPosition(Player* bot, float& x, float& y, float& z, 
                                         bool allowFly, bool allowSwim)
{
    if (!bot || !bot->GetMap())
        return false;
    
    // 首先尝试获取地面高度
    float groundHeight = GetSafeGroundHeight(bot, x, y);
    if (groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
    {
        // 如果当前位置无效，尝试附近的位置
        const float searchRadius = 5.0f;
        const int searchPoints = 8;

        for (int i = 0; i < searchPoints; ++i)
        {
            float angle = (2.0f * M_PI * i) / searchPoints;
            float testX = x + cos(angle) * searchRadius;
            float testY = y + sin(angle) * searchRadius;

            float testGroundHeight = GetSafeGroundHeight(bot, testX, testY);
            if (testGroundHeight != BOT_MOVEMENT_INVALID_HEIGHT)
            {
                x = testX;
                y = testY;
                z = testGroundHeight;
                return true;
            }
        }
        return false;
    }
    
    // 检查水位
    float waterLevel = GetWaterLevel(bot, x, y);
    
    // 根据允许的移动类型调整Z坐标
    if (allowFly)
    {
        // 飞行模式：保持当前高度，但不要太低
        z = std::max(z, groundHeight + 2.0f);
    }
    else if (allowSwim && waterLevel != BOT_MOVEMENT_INVALID_HEIGHT && waterLevel > groundHeight)
    {
        // 游泳模式：使用水面高度
        z = waterLevel;
    }
    else
    {
        // 步行模式：使用地面高度
        z = groundHeight;
    }
    
    return true;
}

std::vector<PathPoint> BotTerrainValidator::GenerateSafePath(Player* bot, float startX, float startY, float startZ,
                                                            float endX, float endY, float endZ,
                                                            bool allowFly, bool allowSwim)
{
    std::vector<PathPoint> safePath;
    
    if (!bot || !bot->GetMap())
        return safePath;
    
    // 添加起始点
    float correctedStartX = startX, correctedStartY = startY, correctedStartZ = startZ;
    if (CorrectPosition(bot, correctedStartX, correctedStartY, correctedStartZ, allowFly, allowSwim))
    {
        safePath.push_back(PathPoint(correctedStartX, correctedStartY, correctedStartZ));
    }
    
    // 计算路径
    float dx = endX - startX;
    float dy = endY - startY;
    float totalDistance = std::sqrt(dx * dx + dy * dy);
    
    // 如果距离很短，直接连接
    if (totalDistance < 10.0f)
    {
        float correctedEndX = endX, correctedEndY = endY, correctedEndZ = endZ;
        if (CorrectPosition(bot, correctedEndX, correctedEndY, correctedEndZ, allowFly, allowSwim))
        {
            safePath.push_back(PathPoint(correctedEndX, correctedEndY, correctedEndZ));
        }
        return safePath;
    }
    
    // 生成中间路径点
    const int pathSegments = std::min(10, static_cast<int>(totalDistance / 5.0f));
    for (int i = 1; i < pathSegments; ++i)
    {
        float ratio = static_cast<float>(i) / pathSegments;
        float midX = startX + dx * ratio;
        float midY = startY + dy * ratio;
        float midZ = startZ + (endZ - startZ) * ratio;
        
        if (CorrectPosition(bot, midX, midY, midZ, allowFly, allowSwim))
        {
            safePath.push_back(PathPoint(midX, midY, midZ));
        }
    }
    
    // 添加终点
    float correctedEndX = endX, correctedEndY = endY, correctedEndZ = endZ;
    if (CorrectPosition(bot, correctedEndX, correctedEndY, correctedEndZ, allowFly, allowSwim))
    {
        safePath.push_back(PathPoint(correctedEndX, correctedEndY, correctedEndZ));
    }
    
    return safePath;
}

// 私有方法实现
TerrainType BotTerrainValidator::GetTerrainType(Player* bot, float x, float y, float z)
{
    if (!bot || !bot->GetMap())
        return TERRAIN_INVALID;

    float groundHeight = bot->GetMap()->GetHeight(x, y, BOT_MOVEMENT_MAX_HEIGHT);
    if (groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
        return TERRAIN_INVALID;

    float waterLevel = GetWaterLevel(bot, x, y);

    // 判断地形类型
    if (waterLevel != BOT_MOVEMENT_INVALID_HEIGHT && z <= waterLevel + BOT_MOVEMENT_WATER_TOLERANCE)
    {
        return TERRAIN_WATER;
    }
    else if (std::abs(z - groundHeight) <= BOT_MOVEMENT_GROUND_TOLERANCE)
    {
        return TERRAIN_GROUND;
    }
    else if (z > groundHeight + BOT_MOVEMENT_GROUND_TOLERANCE)
    {
        return TERRAIN_AIR;
    }
    else if (z < groundHeight - BOT_MOVEMENT_GROUND_TOLERANCE)
    {
        return TERRAIN_UNDERGROUND;
    }

    return TERRAIN_UNKNOWN;
}




float BotTerrainValidator::CalculateSlopeAngle(float x1, float y1, float z1, float x2, float y2, float z2)
{
    float horizontalDistance = CalculateDistance2D(x1, y1, x2, y2);
    if (horizontalDistance < 0.1f)
        return 0.0f;

    float verticalDistance = std::abs(z2 - z1);
    float angleRadians = std::atan(verticalDistance / horizontalDistance);
    return angleRadians * 180.0f / M_PI; // 转换为度
}

PathPoint BotTerrainValidator::GetNearestValidPosition(Player* bot, float x, float y, float z, float searchRadius)
{
    PathPoint result;

    if (!bot || !bot->GetMap())
        return result;

    // 首先检查当前位置
    auto validation = ValidatePosition(bot, x, y, z);
    if (validation.isValid)
    {
        result = PathPoint(x, y, z);
        return result;
    }

    // 搜索附近的有效位置
    const int searchPoints = 16;
    const int radiusSteps = 3;

    for (int r = 1; r <= radiusSteps; ++r)
    {
        float currentRadius = searchRadius * r / radiusSteps;

        for (int i = 0; i < searchPoints; ++i)
        {
            float angle = (2.0f * M_PI * i) / searchPoints;
            float testX = x + cos(angle) * currentRadius;
            float testY = y + sin(angle) * currentRadius;
            float testZ = z;

            // 尝试修正位置
            if (CorrectPosition(bot, testX, testY, testZ))
            {
                auto testValidation = ValidatePosition(bot, testX, testY, testZ);
                if (testValidation.isValid)
                {
                    result = PathPoint(testX, testY, testZ);
                    return result;
                }
            }
        }
    }

    return result; // 返回无效位置
}

bool BotTerrainValidator::IsWithinMapBounds(Player* bot, float x, float y)
{
    if (!bot || !bot->GetMap())
        return false;

    // 检查基本坐标范围
    if (!IsValidCoordinate(x, y))
        return false;

    // 这里可以添加更具体的地图边界检查
    // 目前使用基本的坐标范围检查
    return true;
}

bool BotTerrainValidator::IsHeightDifferenceValid(float startZ, float endZ, float distance)
{
    if (distance < 0.1f)
        return true;

    float heightDiff = std::abs(endZ - startZ);
    float maxAllowedHeight = distance * std::tan(BOT_MOVEMENT_MAX_SLOPE_ANGLE * M_PI / 180.0f);

    return heightDiff <= maxAllowedHeight;
}

void BotTerrainValidator::LogValidationResult(Player* bot, const TerrainValidationResult& result)
{
    if (logLevel == LOG_NONE)
        return;

    if (!result.isValid && logLevel >= LOG_WARNING)
    {
        LOG_WARN("movement", "BotTerrainValidator: 位置验证失败 - 机器人: {}, 错误: {}",
                 bot ? bot->GetName() : "Unknown", result.errorMessage);
    }
    else if (result.isValid && logLevel >= LOG_DEBUG)
    {
        LOG_DEBUG("movement", "BotTerrainValidator: 位置验证成功 - 机器人: {}, 地形类型: {}",
                  bot ? bot->GetName() : "Unknown", static_cast<int>(result.terrainType));
    }
}

void BotTerrainValidator::LogPathValidationResult(Player* bot, const PathValidationResult& result)
{
    if (logLevel == LOG_NONE)
        return;

    if (!result.isValid && logLevel >= LOG_WARNING)
    {
        LOG_WARN("movement", "BotTerrainValidator: 路径验证失败 - 机器人: {}, 距离: {:.2f}, 错误: {}",
                 bot ? bot->GetName() : "Unknown", result.totalDistance, result.errorMessage);
    }
    else if (result.isValid && logLevel >= LOG_DEBUG)
    {
        LOG_DEBUG("movement", "BotTerrainValidator: 路径验证成功 - 机器人: {}, 距离: {:.2f}, 路径点数: {}",
                  bot ? bot->GetName() : "Unknown", result.totalDistance, result.validPath.size());
    }
}

bool BotTerrainValidator::IsSlopeWalkable(Player* bot, float x1, float y1, float z1, float x2, float y2, float z2)
{
    float slopeAngle = CalculateSlopeAngle(x1, y1, z1, x2, y2, z2);
    return slopeAngle <= BOT_MOVEMENT_MAX_SLOPE_ANGLE;
}

bool BotTerrainValidator::HasObstacles(Player* bot, float x1, float y1, float z1, float x2, float y2, float z2)
{
    if (!bot || !bot->GetMap())
        return true;

    // 简单的障碍物检测 - 检查路径中间点
    const int checkPoints = 5;
    for (int i = 1; i < checkPoints; ++i)
    {
        float ratio = static_cast<float>(i) / checkPoints;
        float checkX = x1 + (x2 - x1) * ratio;
        float checkY = y1 + (y2 - y1) * ratio;
        float checkZ = z1 + (z2 - z1) * ratio;

        float groundHeight = bot->GetMap()->GetHeight(checkX, checkY, BOT_MOVEMENT_MAX_HEIGHT);
        if (groundHeight == BOT_MOVEMENT_INVALID_HEIGHT)
            return true;

        // 如果目标高度与地面高度差异太大，可能有障碍物
        if (std::abs(checkZ - groundHeight) > BOT_MOVEMENT_MAX_STEP_HEIGHT)
            return true;
    }

    return false;
}



float BotTerrainValidator::GetWaterLevel(Player* bot, float x, float y)
{
    if (!bot || !bot->GetMap())
        return BOT_MOVEMENT_INVALID_HEIGHT;

    // 尝试获取水位高度
    float waterLevel = bot->GetMap()->GetWaterLevel(x, y);
    if (waterLevel == BOT_MOVEMENT_INVALID_HEIGHT)
        return BOT_MOVEMENT_INVALID_HEIGHT;

    return waterLevel;
}

bool BotTerrainValidator::IsInsideBuilding(Player* bot, float x, float y, float z)
{
    if (!bot || !bot->GetMap())
        return false;

    // 简单的建筑物检测 - 检查上方是否有遮挡
    float ceilingHeight = bot->GetMap()->GetHeight(x, y, z + 10.0f);
    if (ceilingHeight != BOT_MOVEMENT_INVALID_HEIGHT && ceilingHeight < z + 3.0f)
    {
        return true; // 可能在建筑物内
    }

    return false;
}
