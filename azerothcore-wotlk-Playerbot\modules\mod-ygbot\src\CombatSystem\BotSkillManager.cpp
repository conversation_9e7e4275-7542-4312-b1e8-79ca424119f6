#include "BotSkillManager.h"
#include "Player.h"
#include "SpellMgr.h"
#include "SpellInfo.h"
#include "Log.h"
#include "Config.h"
#include "DBCStores.h"
#include "../PlayerPatch.h"
#include "../TalentSystem/YGbotTalentManager.h"

// 单例实例
BotSkillManager* BotSkillManager::_instance = nullptr;

BotSkillManager* BotSkillManager::instance()
{
    if (!_instance)
        _instance = new BotSkillManager();
    return _instance;
}

void BotSkillManager::Initialize()
{
    LOG_INFO("server.loading", "初始化机器人技能管理器...");

    // 检测是否存在mod-learnspells模块
    _hasLearnSpellsModule = DetectLearnSpellsModule();

    if (_hasLearnSpellsModule)
    {
        LOG_INFO("server.loading", "📝 检测到mod-learnspells模块，启用兼容模式");
        LOG_INFO("server.loading", "🔧 本管理器将专注于武器技能和特殊技能补充");
    }
    else
    {
        LOG_INFO("server.loading", "📝 未检测到mod-learnspells模块，启用完整模式");
        LOG_INFO("server.loading", "🔧 本管理器将负责所有技能学习");
    }

    // 加载配置
    LoadClassConfigs();

    LOG_INFO("server.loading", "机器人技能管理器初始化完成 (模式: {})",
             _hasLearnSpellsModule ? "兼容" : "完整");
}

void BotSkillManager::Shutdown()
{
    _classConfigs.clear();
    
    if (_instance)
    {
        delete _instance;
        _instance = nullptr;
    }
    
    LOG_INFO("server.loading", "机器人技能管理器已关闭");
}

void BotSkillManager::LearnSkillsOnLogin(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 检查是否启用登录时学习技能
    if (!sConfigMgr->GetOption<bool>("YGbot.SkillManager.LearnOnLogin", true))
    {
        LOG_DEBUG("skills", "机器人 {} 登录技能学习已禁用", bot->GetName());
        return;
    }

    LOG_INFO("skills", "机器人 {} 登录时学习技能 (等级: {}, 职业: {}, 阵营: {})",
             bot->GetName(), bot->GetLevel(), bot->getClass(),
             bot->GetTeamId() == TEAM_ALLIANCE ? "联盟" : "部落");

    // 1. 学习职业技能
    LearnClassSpells(bot);

    // 2. 学习武器技能
    if (sConfigMgr->GetOption<bool>("YGbot.SkillManager.EnableWeaponSkills", true))
    {
        LearnWeaponSkills(bot);
    }

    // 3. 学习等级技能 (从1级到当前等级)
    LearnLevelSpells(bot, 1, bot->GetLevel());

    // 4. 学习额外技能 (阵营特定)
    LearnAdditionalSpells(bot);

    // 5. 确保基础技能
    EnsureBasicSkills(bot);

    LOG_INFO("skills", "机器人 {} 登录技能学习完成", bot->GetName());
}

void BotSkillManager::LearnSkillsOnLevelUp(Player* bot, uint8 newLevel)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 检查是否启用升级时学习技能
    if (!sConfigMgr->GetOption<bool>("YGbot.SkillManager.LearnOnLevelUp", true))
    {
        LOG_DEBUG("skills", "机器人 {} 升级技能学习已禁用", bot->GetName());
        return;
    }

    LOG_INFO("skills", "🆙 机器人 {} 升级到 {} 级，学习新技能", bot->GetName(), newLevel);

    // 学习新等级的技能
    LearnLevelSpells(bot, newLevel, newLevel);

    // 学习新等级的额外技能
    LearnAdditionalSpells(bot);

    // 更新武器技能等级
    if (sConfigMgr->GetOption<bool>("YGbot.SkillManager.EnableWeaponSkills", true))
    {
        UpdateWeaponSkillLevels(bot, newLevel);
    }
}

void BotSkillManager::LearnSkillsForTalentSpec(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 根据天赋特化学习技能 
    LOG_DEBUG("skills", "机器人 {} 根据天赋学习技能", bot->GetName());

    // 这里可以根据天赋点数来学习特定的技能
    // 暂时使用基础实现
    LearnClassSpells(bot);
}

void BotSkillManager::EnsureBasicSkills(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 确保机器人学会基础技能
    uint8 classId = bot->getClass();

    // 为萨满添加图腾
    if (classId == CLASS_SHAMAN)
    {
        bot->AddItem(5175, 1); // 土图腾
        bot->AddItem(5176, 1); // 火图腾
        bot->AddItem(5177, 1); // 水图腾
        bot->AddItem(5178, 1); // 风图腾
    }

    LOG_DEBUG("skills", "机器人 {} 基础技能检查完成", bot->GetName());
}

void BotSkillManager::LearnClassSpells(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    if (_hasLearnSpellsModule)
    {
        // 兼容模式：只学习特殊技能，避免冲突
        LOG_INFO("skills", "兼容模式：为机器人 {} 补充特殊技能", bot->GetName());

        uint32 learnedCount = 0;
        std::vector<uint32> specialSpells = GetSpecialSpellsForClass(bot->getClass());

        for (uint32 spellId : specialSpells)
        {
            const SpellInfo* spellInfo = sSpellMgr->GetSpellInfo(spellId);
            if (!spellInfo)
                continue;

            if (spellInfo->BaseLevel > bot->GetLevel())
                continue;

            if (!CheckFactionRestriction(bot, spellInfo))
                continue;

            if (!bot->HasSpell(spellId))
            {
                bot->learnSpell(spellId);
                learnedCount++;
                LOG_DEBUG("skills", "补充特殊技能: {} [{}级]", spellId, spellInfo->BaseLevel);
            }
        }

        LOG_INFO("skills", "兼容模式完成，补充 {} 个特殊技能", learnedCount);
    }
    else
    {
        // 完整模式：学习所有职业技能
        LOG_INFO("skills", "完整模式：为机器人 {} 学习所有职业技能", bot->GetName());
        LearnAllClassSpells(bot);
    }
}

void BotSkillManager::LearnWeaponSkills(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    uint8 classId = bot->getClass();
    uint8 raceId = bot->getRace();
    uint32 learnedCount = 0;

    LOG_INFO("skills", "🗡️ 开始为机器人 {} 学习武器技能 (职业: {}, 种族: {})",
             bot->GetName(), classId, raceId);

    // 根据职业学习武器技能
    std::vector<uint32> weaponSkills = GetWeaponSkillsForClass(classId);

    for (uint32 skillId : weaponSkills)
    {
        if (!bot->HasSkill(skillId))
        {
            // 学习武器技能
            bot->SetSkill(skillId, 0, bot->GetLevel() * 5, bot->GetLevel() * 5);
            learnedCount++;

            LOG_DEBUG("skills", "机器人 {} 学习了武器技能: {}", bot->GetName(), skillId);
        }
    }

    // 根据种族学习额外武器技能
    std::vector<uint32> racialWeapons = GetRacialWeaponSkills(raceId);

    for (uint32 skillId : racialWeapons)
    {
        if (!bot->HasSkill(skillId))
        {
            bot->SetSkill(skillId, 0, bot->GetLevel() * 5, bot->GetLevel() * 5);
            learnedCount++;

            LOG_DEBUG("skills", "机器人 {} 学习了种族武器技能: {}", bot->GetName(), skillId);
        }
    }

    LOG_INFO("skills", "机器人 {} 武器技能学习完成，共学习 {} 个武器技能",
             bot->GetName(), learnedCount);
}

void BotSkillManager::LearnLevelSpells(Player* bot, uint8 fromLevel, uint8 toLevel)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    uint8 classId = bot->getClass();
    auto configIt = _classConfigs.find(classId);
    if (configIt == _classConfigs.end())
        return;

    const ClassSpellConfig& config = configIt->second;

    LOG_DEBUG("skills", "为机器人 {} 学习等级技能 ({}-{}级)", bot->GetName(), fromLevel, toLevel);

    // 学习配置中的等级技能
    for (const LevelSpellConfig& spellConfig : config.levelSpells)
    {
        if (spellConfig.minLevel >= fromLevel && spellConfig.minLevel <= toLevel)
        {
            if (!bot->HasSpell(spellConfig.spellId))
            {
                bot->learnSpell(spellConfig.spellId);
                LOG_DEBUG("skills", "机器人 {} 学习了等级技能: {} ({}级{})",
                         bot->GetName(), spellConfig.spellId, spellConfig.minLevel,
                         spellConfig.isImportant ? " [重要]" : "");
            }
        }
    }
}

void BotSkillManager::LearnAdditionalSpells(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    uint8 classId = bot->getClass();
    uint8 botLevel = bot->GetLevel();

    auto configIt = _classConfigs.find(classId);
    if (configIt == _classConfigs.end())
        return;

    const ClassSpellConfig& config = configIt->second;

    // 学习额外技能
    for (const auto& pair : config.additionalSpells)
    {
        uint8 requiredLevel = pair.first;
        if (botLevel >= requiredLevel)
        {
            for (uint32 spellId : pair.second)
            {
                if (!bot->HasSpell(spellId))
                {
                    bot->learnSpell(spellId);
                    LOG_DEBUG("skills", "机器人 {} 学习了额外技能: {} ({}级)",
                             bot->GetName(), spellId, requiredLevel);
                }
            }
        }
    }
}

bool BotSkillManager::HasRequiredSkills(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return true;

    std::vector<uint32> missingSkills = GetMissingSkills(bot);
    return missingSkills.empty();
}

std::vector<uint32> BotSkillManager::GetMissingSkills(Player* bot)
{
    std::vector<uint32> missingSkills;

    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return missingSkills;

    uint8 classId = bot->getClass();
    uint8 botLevel = bot->GetLevel();

    auto configIt = _classConfigs.find(classId);
    if (configIt == _classConfigs.end())
        return missingSkills;

    const ClassSpellConfig& config = configIt->second;

    // 检查等级技能
    for (const LevelSpellConfig& spellConfig : config.levelSpells)
    {
        if (spellConfig.minLevel <= botLevel && !bot->HasSpell(spellConfig.spellId))
        {
            missingSkills.push_back(spellConfig.spellId);
        }
    }

    // 检查额外技能
    for (const auto& pair : config.additionalSpells)
    {
        uint8 requiredLevel = pair.first;
        if (botLevel >= requiredLevel)
        {
            for (uint32 spellId : pair.second)
            {
                if (!bot->HasSpell(spellId))
                {
                    missingSkills.push_back(spellId);
                }
            }
        }
    }

    return missingSkills;
}

uint32 BotSkillManager::GetSpellFamily(const Player* player)
{
    switch (player->getClass())
    {
        case CLASS_ROGUE:
            return SPELLFAMILY_ROGUE;
        case CLASS_DEATH_KNIGHT:
            return SPELLFAMILY_DEATHKNIGHT;
        case CLASS_WARRIOR:
            return SPELLFAMILY_WARRIOR;
        case CLASS_PRIEST:
            return SPELLFAMILY_PRIEST;
        case CLASS_MAGE:
            return SPELLFAMILY_MAGE;
        case CLASS_PALADIN:
            return SPELLFAMILY_PALADIN;
        case CLASS_HUNTER:
            return SPELLFAMILY_HUNTER;
        case CLASS_DRUID:
            return SPELLFAMILY_DRUID;
        case CLASS_SHAMAN:
            return SPELLFAMILY_SHAMAN;
        case CLASS_WARLOCK:
            return SPELLFAMILY_WARLOCK;
        default:
            return SPELLFAMILY_GENERIC;
    }
}

bool BotSkillManager::IsIgnoredSpell(uint32 spellId)
{
    // 参考mod-learnspells模块的忽略技能列表
    static std::unordered_set<uint32> ignoredSpells = {
        // 宠物技能和特殊技能
        50581, 30708, 48076, 62900, 62901, 62902, 59671, 50589, 66906, 66907,
        24131, 23455, 23458, 23459, 27803, 27804, 27805, 25329, 48075, 42243,
        42244, 42245, 42234, 58432, 58433, 65878, 18848, 16979, 49376, 54055,
        20647, 42243, 24131, 45470,

        // 印记和封印技能
        31898, 31804, 53733, 31803, 53742, 53725, 53726,

        // 特殊技能
        1804, 348, 1455, 1456, 11687, 11688, 11689, 27222, 57946, 25306,
        53652, 53653, 53654, 7328, 10322, 10324, 20772, 20773, 48949, 48950,
        33878, 33876, 33982, 33986, 33987, 33983, 48563, 48565, 48566, 48564,

        // 达拉然技能
        61316, 61024,

        // 装饰性技能
        28271, 28272, 61025, 61305, 61721, 61780,

        // 可选任务技能
        18540,
    };

    return ignoredSpells.find(spellId) != ignoredSpells.end();
}

bool BotSkillManager::IsValidClassSpell(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    // 检查技能线能力
    SkillLineAbilityMapBounds bounds = sSpellMgr->GetSkillLineAbilityMapBounds(spellInfo->Id);

    for (auto itr = bounds.first; itr != bounds.second; ++itr)
    {
        if (itr->second->Spell == spellInfo->Id &&
            itr->second->RaceMask == 0 &&
            itr->second->AcquireMethod == 0)
        {
            return true;
        }
    }

    return false;
}

bool BotSkillManager::ShouldLearnSpell(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    uint8 botLevel = player->GetLevel();
    uint32 spellId = spellInfo->Id;

    // 1. 检查是否被忽略
    if (IsIgnoredSpell(spellId))
        return false;

    // 2. 检查等级要求
    if (spellInfo->BaseLevel > botLevel)
        return false;

    // 3. 智能检查阵营限制
    if (!CheckFactionRestriction(player, spellInfo))
        return false;

    // 4. 跳过宠物技能
    if (spellInfo->PowerType == POWER_FOCUS)
        return false;

    // 5. 跳过被动技能（除非是重要的被动技能）
    if (spellInfo->IsPassive() && !IsImportantPassiveSpell(spellId))
        return false;

    // 6. 检查是否是有效的职业技能
    if (!IsValidClassSpell(player, spellInfo))
        return false;

    // 7. 检查前置技能
    SpellInfo const* prevSpell = spellInfo->GetPrevRankSpell();
    if (prevSpell && !player->HasSpell(prevSpell->Id))
        return false;

    // 8. 检查技能等级（只学习最高等级的技能）
    if (!IsHighestRankSpell(player, spellInfo))
        return false;

    // 9. 检查天赋要求（如果有的话）
    if (!MeetsTalentRequirements(player, spellInfo))
        return false;

    return true;
}

void BotSkillManager::LoadClassConfigs()
{
    LOG_INFO("skills", "🔧 加载智能职业技能配置...");

    // 新的智能系统不需要手动配置每个技能
    // 只需要配置特殊情况和忽略列表
    LoadSpecialSkillConfigs();

    LOG_INFO("skills", "智能职业技能配置加载完成");
}

void BotSkillManager::LoadSpecialSkillConfigs()
{
    // 为每个职业配置特殊技能和忽略列表

    // 战士特殊配置
    ClassSpellConfig& warriorConfig = _classConfigs[CLASS_WARRIOR];
    AddAdditionalSpell(warriorConfig, 6, 3127);     // 招架
    AddAdditionalSpell(warriorConfig, 20, 674);     // 双持
    AddAdditionalSpell(warriorConfig, 40, 750);     // 板甲专精

    // 圣骑士特殊配置
    ClassSpellConfig& paladinConfig = _classConfigs[CLASS_PALADIN];
    AddAdditionalSpell(paladinConfig, 8, 3127);     // 招架
    AddAdditionalSpell(paladinConfig, 40, 750);     // 板甲专精
    AddAdditionalSpell(paladinConfig, 20, 23214);   // 召唤战马 (联盟)
    AddAdditionalSpell(paladinConfig, 20, 34767);   // 召唤战马 (部落)

    // 猎人特殊配置
    ClassSpellConfig& hunterConfig = _classConfigs[CLASS_HUNTER];
    AddAdditionalSpell(hunterConfig, 8, 3127);      // 招架
    AddAdditionalSpell(hunterConfig, 20, 674);      // 双持
    AddAdditionalSpell(hunterConfig, 40, 8737);     // 锁甲专精

    // 盗贼特殊配置
    ClassSpellConfig& rogueConfig = _classConfigs[CLASS_ROGUE];
    AddAdditionalSpell(rogueConfig, 12, 3127);      // 招架
    AddAdditionalSpell(rogueConfig, 20, 674);       // 双持
    AddAdditionalSpell(rogueConfig, 40, 8737);      // 皮甲专精

    // 牧师特殊配置
    ClassSpellConfig& priestConfig = _classConfigs[CLASS_PRIEST];
    AddAdditionalSpell(priestConfig, 40, 8737);     // 布甲专精

    // 萨满特殊配置
    ClassSpellConfig& shamanConfig = _classConfigs[CLASS_SHAMAN];
    AddAdditionalSpell(shamanConfig, 40, 8737);     // 锁甲专精
    AddAdditionalSpell(shamanConfig, 70, 2825);     // 血性狂暴 (部落)
    AddAdditionalSpell(shamanConfig, 70, 32182);    // 英勇 (联盟)

    // 法师特殊配置
    ClassSpellConfig& mageConfig = _classConfigs[CLASS_MAGE];
    AddAdditionalSpell(mageConfig, 40, 8737);       // 布甲专精
    // 传送门技能会通过智能系统自动学习

    // 术士特殊配置
    ClassSpellConfig& warlockConfig = _classConfigs[CLASS_WARLOCK];
    AddAdditionalSpell(warlockConfig, 40, 8737);    // 布甲专精

    // 德鲁伊特殊配置
    ClassSpellConfig& druidConfig = _classConfigs[CLASS_DRUID];
    AddAdditionalSpell(druidConfig, 40, 8737);      // 皮甲专精

    // 死亡骑士特殊配置
    ClassSpellConfig& dkConfig = _classConfigs[CLASS_DEATH_KNIGHT];
    AddAdditionalSpell(dkConfig, 55, 750);          // 板甲专精

    LOG_DEBUG("skills", "特殊技能配置完成");
}


void BotSkillManager::AddLevelSpell(ClassSpellConfig& config, uint32 spellId, uint8 level, bool important)
{
    LevelSpellConfig spellConfig(spellId, level, 80, important);
    config.levelSpells.push_back(spellConfig);
}

void BotSkillManager::AddAdditionalSpell(ClassSpellConfig& config, uint8 level, uint32 spellId)
{
    config.additionalSpells[level].push_back(spellId);
}

void BotSkillManager::AddIgnoredSpell(ClassSpellConfig& config, uint32 spellId)
{
    config.ignoredSpells.insert(spellId);
}

bool BotSkillManager::IsImportantPassiveSpell(uint32 spellId)
{
    // 重要的被动技能列表（专精、天赋等）
    static std::unordered_set<uint32> importantPassives = {
        // 武器专精
        674,    // 双持
        750,    // 板甲专精
        8737,   // 锁甲专精/皮甲专精/布甲专精
        3127,   // 招架

        // 职业特性
        12678,  // 姿态掌握 (战士)
        20719,  // 猫科动物优雅 (德鲁伊)
        62600,  // 野蛮防御 (德鲁伊)

        // 其他重要被动技能
        // 可以根据需要添加更多
    };

    return importantPassives.find(spellId) != importantPassives.end();
}

bool BotSkillManager::IsHighestRankSpell(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    // 检查是否有更高等级的同名技能
    SpellInfo const* nextSpell = spellInfo->GetNextRankSpell();
    if (nextSpell)
    {
        // 如果有更高等级的技能，且机器人等级足够学习，则不学习当前等级
        if (nextSpell->BaseLevel <= player->GetLevel())
            return false;
    }

    return true;
}

bool BotSkillManager::MeetsTalentRequirements(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    uint32 spellId = spellInfo->Id;
    uint8 playerClass = player->getClass();
    uint8 activeSpec = player->GetActiveSpec();

    // 检查是否是天赋技能
    if (IsTalentSpell(spellId))
    {
        // 如果是天赋技能，检查玩家是否学会了对应的天赋
        return player->HasTalent(spellId, activeSpec);
    }

    // 检查特定技能的天赋前置要求
    if (HasSpecificTalentRequirements(playerClass, spellId))
    {
        return CheckSpecificTalentRequirements(player, spellId);
    }

    // 检查天赋树深度要求（某些技能需要在特定天赋树投入一定点数）
    if (HasTalentTreeRequirements(playerClass, spellId))
    {
        return CheckTalentTreeRequirements(player, spellId);
    }

    // 对于大部分基础技能，不需要天赋要求
    return true;
}

std::vector<uint32> BotSkillManager::GetWeaponSkillsForClass(uint8 classId)
{
    std::vector<uint32> skills;

    switch (classId)
    {
        case CLASS_WARRIOR:
            skills = {
                SKILL_SWORDS, SKILL_AXES, SKILL_MACES, SKILL_2H_SWORDS,
                SKILL_2H_AXES, SKILL_2H_MACES, SKILL_POLEARMS, SKILL_DAGGERS,
                SKILL_BOWS, SKILL_GUNS, SKILL_CROSSBOWS, SKILL_THROWN,
                SKILL_DEFENSE, SKILL_SHIELD, SKILL_UNARMED
            };
            break;

        case CLASS_PALADIN:
            skills = {
                SKILL_SWORDS, SKILL_AXES, SKILL_MACES, SKILL_2H_SWORDS,
                SKILL_2H_AXES, SKILL_2H_MACES, SKILL_POLEARMS,
                SKILL_DEFENSE, SKILL_SHIELD, SKILL_UNARMED
            };
            break;

        case CLASS_HUNTER:
            skills = {
                SKILL_SWORDS, SKILL_AXES, SKILL_DAGGERS, SKILL_POLEARMS,
                SKILL_2H_AXES, SKILL_2H_SWORDS, SKILL_STAVES,
                SKILL_BOWS, SKILL_GUNS, SKILL_CROSSBOWS,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_ROGUE:
            skills = {
                SKILL_SWORDS, SKILL_DAGGERS, SKILL_MACES, SKILL_BOWS,
                SKILL_GUNS, SKILL_CROSSBOWS, SKILL_THROWN,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_PRIEST:
            skills = {
                SKILL_DAGGERS, SKILL_STAVES, SKILL_WANDS,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_SHAMAN:
            skills = {
                SKILL_AXES, SKILL_MACES, SKILL_2H_AXES, SKILL_2H_MACES,
                SKILL_STAVES, SKILL_DAGGERS, SKILL_UNARMED,
                SKILL_DEFENSE, SKILL_SHIELD
            };
            break;

        case CLASS_MAGE:
            skills = {
                SKILL_DAGGERS, SKILL_SWORDS, SKILL_STAVES, SKILL_WANDS,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_WARLOCK:
            skills = {
                SKILL_DAGGERS, SKILL_SWORDS, SKILL_STAVES, SKILL_WANDS,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_DRUID:
            skills = {
                SKILL_MACES, SKILL_2H_MACES, SKILL_DAGGERS, SKILL_STAVES,
                SKILL_POLEARMS, SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        case CLASS_DEATH_KNIGHT:
            skills = {
                SKILL_SWORDS, SKILL_AXES, SKILL_MACES, SKILL_2H_SWORDS,
                SKILL_2H_AXES, SKILL_2H_MACES, SKILL_POLEARMS,
                SKILL_DEFENSE, SKILL_UNARMED
            };
            break;

        default:
            skills = { SKILL_DEFENSE, SKILL_UNARMED };
            break;
    }

    return skills;
}

std::vector<uint32> BotSkillManager::GetRacialWeaponSkills(uint8 raceId)
{
    std::vector<uint32> skills;

    switch (raceId)
    {
        case RACE_HUMAN:
            skills = { SKILL_SWORDS, SKILL_MACES };
            break;
        case RACE_DWARF:
            skills = { SKILL_GUNS, SKILL_MACES };
            break;
        case RACE_NIGHTELF:
            skills = { SKILL_BOWS };
            break;
        case RACE_GNOME:
            skills = { SKILL_DAGGERS };
            break;
        case RACE_DRAENEI:
            skills = { SKILL_MACES };
            break;
        case RACE_ORC:
            skills = { SKILL_AXES, SKILL_2H_AXES };
            break;
        case RACE_UNDEAD_PLAYER:
            skills = { SKILL_DAGGERS };
            break;
        case RACE_TAUREN:
            skills = { SKILL_2H_MACES };
            break;
        case RACE_TROLL:
            skills = { SKILL_BOWS, SKILL_THROWN };
            break;
        case RACE_BLOODELF:
            skills = { SKILL_SWORDS };
            break;
        default:
            break;
    }

    return skills;
}

bool BotSkillManager::CheckFactionRestriction(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    uint32 spellId = spellInfo->Id;
    TeamId teamId = player->GetTeamId();

    // 1. 检查基础阵营属性
    if ((spellInfo->AttributesEx7 & SPELL_ATTR7_ALLIANCE_SPECIFIC_SPELL && teamId != TEAM_ALLIANCE) ||
        (spellInfo->AttributesEx7 & SPELL_ATTR7_HORDE_SPECIFIC_SPELL && teamId != TEAM_HORDE))
        return false;

    // 2. 检查特定的阵营限制技能
    static std::unordered_map<uint32, TeamId> factionSpells = {
        // 联盟专属技能
        { 3561, TEAM_ALLIANCE },   // 传送：暴风城
        { 3565, TEAM_ALLIANCE },   // 传送：达纳苏斯
        { 32271, TEAM_ALLIANCE },  // 传送：埃索达
        { 49359, TEAM_ALLIANCE },  // 传送：塞拉摩
        { 10059, TEAM_ALLIANCE },  // 传送门：暴风城
        { 11419, TEAM_ALLIANCE },  // 传送门：达纳苏斯
        { 32266, TEAM_ALLIANCE },  // 传送门：埃索达
        { 23214, TEAM_ALLIANCE },  // 召唤战马 (联盟)
        { 13819, TEAM_ALLIANCE },  // 召唤战马 (联盟)
        { 33690, TEAM_ALLIANCE },  // 传送：沙塔斯 (联盟)
        { 33691, TEAM_ALLIANCE },  // 传送门：沙塔斯 (联盟)
        { 32182, TEAM_ALLIANCE },  // 英勇 (联盟)

        // 部落专属技能
        { 3567, TEAM_HORDE },      // 传送：奥格瑞玛
        { 3566, TEAM_HORDE },      // 传送：雷霆崖
        { 32272, TEAM_HORDE },     // 传送：银月城
        { 49358, TEAM_HORDE },     // 传送：石爪山
        { 11417, TEAM_HORDE },     // 传送门：奥格瑞玛
        { 11420, TEAM_HORDE },     // 传送门：雷霆崖
        { 32267, TEAM_HORDE },     // 传送门：银月城
        { 34767, TEAM_HORDE },     // 召唤战马 (部落)
        { 34769, TEAM_HORDE },     // 召唤战马 (部落)
        { 35715, TEAM_HORDE },     // 传送：沙塔斯 (部落)
        { 35717, TEAM_HORDE },     // 传送门：沙塔斯 (部落)
        { 2825, TEAM_HORDE },      // 嗜血 (部落)
    };

    auto it = factionSpells.find(spellId);
    if (it != factionSpells.end())
    {
        return it->second == teamId;
    }

    // 3. 检查种族限制
    if (!CheckRaceRestriction(player, spellInfo))
        return false;

    return true;
}

bool BotSkillManager::CheckRaceRestriction(const Player* player, const SpellInfo* spellInfo)
{
    if (!player || !spellInfo)
        return false;

    uint32 spellId = spellInfo->Id;
    uint8 raceId = player->getRace();

    // 检查种族特定技能
    static std::unordered_map<uint32, std::vector<uint8>> raceSpells = {
        // 人类
        { 20599, {RACE_HUMAN} },           // 剑类专精
        { 20864, {RACE_HUMAN} },           // 锤类专精
        { 59752, {RACE_HUMAN} },           // 人类精神

        // 矮人
        { 20596, {RACE_DWARF} },           // 枪械专精
        { 2481, {RACE_DWARF} },            // 寻找宝藏
        { 20594, {RACE_DWARF} },           // 石像形态

        // 暗夜精灵
        { 20583, {RACE_NIGHTELF} },        // 自然抗性
        { 58984, {RACE_NIGHTELF} },        // 影遁
        { 20582, {RACE_NIGHTELF} },        // 精灵之火

        // 侏儒
        { 20593, {RACE_GNOME} },           // 工程学专精
        { 20589, {RACE_GNOME} },           // 逃脱大师

        // 德莱尼
        { 28880, {RACE_DRAENEI} },         // 天赋祝福
        { 59545, {RACE_DRAENEI} },         // 圣光赐福

        // 兽人
        { 20572, {RACE_ORC} },             // 血性狂暴
        { 20573, {RACE_ORC} },             // 硬化皮肤
        { 20574, {RACE_ORC} },             // 斧类专精

        // 亡灵
        { 20577, {RACE_UNDEAD_PLAYER} },   // 亡灵意志
        { 20578, {RACE_UNDEAD_PLAYER} },   // 吞噬
        { 7744, {RACE_UNDEAD_PLAYER} },    // 亡灵意志

        // 牛头人
        { 20549, {RACE_TAUREN} },          // 战争践踏
        { 20550, {RACE_TAUREN} },          // 耐久
        { 20552, {RACE_TAUREN} },          // 草药学

        // 巨魔
        { 20554, {RACE_TROLL} },           // 狂暴
        { 20555, {RACE_TROLL} },           // 再生
        { 26297, {RACE_TROLL} },           // 弓类专精
        { 26290, {RACE_TROLL} },           // 投掷武器专精

        // 血精灵
        { 28877, {RACE_BLOODELF} },        // 奥术洪流
        { 822, {RACE_BLOODELF} },          // 魔法抗性
    };

    auto it = raceSpells.find(spellId);
    if (it != raceSpells.end())
    {
        const std::vector<uint8>& allowedRaces = it->second;
        return std::find(allowedRaces.begin(), allowedRaces.end(), raceId) != allowedRaces.end();
    }

    return true;
}

bool BotSkillManager::DetectLearnSpellsModule()
{
    // 从配置文件读取兼容模式设置
    uint32 compatibilityMode = sConfigMgr->GetOption<uint32>("YGbot.SkillManager.CompatibilityMode", 2);

    LOG_DEBUG("skills", "检测技能学习模块兼容性 (配置模式: {})", compatibilityMode);

    switch (compatibilityMode)
    {
        case 0:
            // 完整模式 - 强制学习所有技能
            LOG_INFO("skills", "配置为完整模式 - 将学习所有职业技能");
            return false;

        case 1:
            // 兼容模式 - 只学习特殊技能
            LOG_INFO("skills", "配置为兼容模式 - 只学习特殊技能");
            return true;

        case 2:
        default:
            // 自动检测模式
            LOG_DEBUG("skills", "自动检测模式 - 检查是否存在其他技能学习模块");

            // 这里可以添加更复杂的检测逻辑
            // 例如：检查是否存在特定的全局变量、函数等
            // 暂时默认为完整模式

            LOG_INFO("skills", "自动检测结果：未发现其他技能学习模块，使用完整模式");
            return false;
    }
}

void BotSkillManager::LearnAllClassSpells(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    uint32 family = GetSpellFamily(bot);
    uint8 botLevel = bot->GetLevel();
    uint32 learnedCount = 0;

    // 获取玩家的天赋专精信息
    std::string specialization = GetPlayerTalentSpecialization(bot);

    LOG_INFO("skills", "🎯 完整模式：为机器人 {} 学习所有职业技能 (等级: {}, 法术族: {}, 专精: {})",
             bot->GetName(), botLevel, family, specialization);

    // 遍历所有法术，学习符合条件的职业技能
    for (uint32 i = 0; i < sSpellMgr->GetSpellInfoStoreSize(); ++i)
    {
        SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(i);

        if (!spellInfo)
            continue;

        // 检查法术族
        if (spellInfo->SpellFamilyName != family)
            continue;

        // 智能判断是否应该学习这个技能
        if (!ShouldLearnSpell(bot, spellInfo))
            continue;

        // 检查是否已经学会
        if (bot->HasSpell(spellInfo->Id))
            continue;

        // 学习技能
        bot->learnSpell(spellInfo->Id);
        learnedCount++;

        LOG_DEBUG("skills", "✅ 学习技能: {} [{}级] {}",
                 spellInfo->Id, spellInfo->BaseLevel,
                 spellInfo->SpellName[0] ? spellInfo->SpellName[0] : "未知技能");
    }

    LOG_INFO("skills", "🎉 完整模式完成，共学习 {} 个职业技能 (专精: {})", learnedCount, specialization);
}

std::vector<uint32> BotSkillManager::GetSpecialSpellsForClass(uint8 classId)
{
    // 返回mod-learnspells可能遗漏的特殊技能
    std::vector<uint32> spells;

    switch (classId)
    {
        case CLASS_WARRIOR:
            spells = {
                3127,   // 招架
                674,    // 双持
                750,    // 板甲专精
                12678,  // 姿态掌握
            };
            break;

        case CLASS_PALADIN:
            spells = {
                3127,   // 招架
                750,    // 板甲专精
                7328,   // 救赎术
            };
            break;

        case CLASS_HUNTER:
            spells = {
                3127,   // 招架
                674,    // 双持
                8737,   // 锁甲专精
                1515,   // 驯服野兽
                883,    // 召唤宠物
                2641,   // 解散野兽
            };
            break;

        case CLASS_ROGUE:
            spells = {
                3127,   // 招架
                674,    // 双持
                8737,   // 皮甲专精
                1804,   // 开锁
                2836,   // 侦测陷阱
            };
            break;

        case CLASS_PRIEST:
            spells = {
                8737,   // 布甲专精
                10880,  // 复活术
            };
            break;

        case CLASS_SHAMAN:
            spells = {
                8737,   // 锁甲专精
                20608,  // 复生
                546,    // 水上行走
            };
            break;

        case CLASS_MAGE:
            spells = {
                8737,   // 布甲专精
                1953,   // 闪现术
                475,    // 驱除魔法
            };
            break;

        case CLASS_WARLOCK:
            spells = {
                8737,   // 布甲专精
                5784,   // 召唤魔马
                688,    // 召唤小鬼
                697,    // 召唤虚空行者
            };
            break;

        case CLASS_DRUID:
            spells = {
                8737,   // 皮甲专精
                5225,   // 追踪人型生物
                783,    // 旅行形态
            };
            break;

        case CLASS_DEATH_KNIGHT:
            spells = {
                750,    // 板甲专精
            };
            break;

        default:
            break;
    }

    return spells;
}

void BotSkillManager::UpdateWeaponSkillLevels(Player* bot, uint8 newLevel)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    LOG_DEBUG("skills", "更新机器人 {} 的武器技能等级到 {} 级", bot->GetName(), newLevel);

    uint8 classId = bot->getClass();
    std::vector<uint32> weaponSkills = GetWeaponSkillsForClass(classId);

    uint32 updatedCount = 0;
    uint32 maxSkillValue = newLevel * 5; // 武器技能最大值 = 等级 * 5

    for (uint32 skillId : weaponSkills)
    {
        if (bot->HasSkill(skillId))
        {
            uint16 currentValue = bot->GetSkillValue(skillId);
            if (currentValue < maxSkillValue)
            {
                bot->SetSkill(skillId, 0, maxSkillValue, maxSkillValue);
                updatedCount++;
                LOG_DEBUG("skills", "更新武器技能 {} 到 {}", skillId, maxSkillValue);
            }
        }
    }

    if (updatedCount > 0)
    {
        LOG_DEBUG("skills", "机器人 {} 更新了 {} 个武器技能", bot->GetName(), updatedCount);
    }
}

bool BotSkillManager::IsTalentSpell(uint32 spellId)
{
    // 检查技能是否是天赋技能
    // 遍历天赋表，查看是否有天赋包含这个技能ID
    for (uint32 i = 0; i < sTalentStore.GetNumRows(); ++i)
    {
        TalentEntry const* talentInfo = sTalentStore.LookupEntry(i);
        if (!talentInfo)
            continue;

        // 检查天赋的所有等级
        for (uint8 rank = 0; rank < MAX_TALENT_RANK; ++rank)
        {
            if (talentInfo->RankID[rank] == spellId)
                return true;
        }
    }

    return false;
}

bool BotSkillManager::HasSpecificTalentRequirements(uint8 playerClass, uint32 spellId)
{
    // 定义需要特定天赋的技能
    static std::unordered_map<uint8, std::unordered_set<uint32>> classTalentRequiredSpells = {
        // 战士 - 某些高级技能需要特定天赋
        {CLASS_WARRIOR, {
            // 例如：某些战斗技能需要特定天赋
        }},

        // 圣骑士 - 某些印记和审判技能需要天赋
        {CLASS_PALADIN, {
            // 例如：某些高级印记需要天赋
        }},

        // 法师 - 某些高级传送门可能需要天赋
        {CLASS_MAGE, {
            // 例如：某些传送门技能需要天赋
        }},

        // 其他职业可以根据需要添加...
    };

    auto classIt = classTalentRequiredSpells.find(playerClass);
    if (classIt != classTalentRequiredSpells.end())
    {
        return classIt->second.find(spellId) != classIt->second.end();
    }

    return false;
}

bool BotSkillManager::CheckSpecificTalentRequirements(const Player* player, uint32 spellId)
{
    if (!player)
        return false;

    uint8 playerClass = player->getClass();
    uint8 activeSpec = player->GetActiveSpec();

    // 根据职业和技能ID检查特定的天赋要求
    switch (playerClass)
    {
        case CLASS_WARRIOR:
            return CheckWarriorTalentRequirements(player, spellId, activeSpec);
        case CLASS_PALADIN:
            return CheckPaladinTalentRequirements(player, spellId, activeSpec);
        case CLASS_MAGE:
            return CheckMageTalentRequirements(player, spellId, activeSpec);
        // 可以为其他职业添加更多检查
        default:
            return true; // 默认允许
    }
}

bool BotSkillManager::HasTalentTreeRequirements(uint8 playerClass, uint32 spellId)
{
    // 定义需要天赋树深度的技能
    // 例如：某些技能需要在特定天赋树投入至少X点天赋

    // 暂时简化实现，后续可以根据需要添加具体的天赋树要求
    return false;
}

bool BotSkillManager::CheckTalentTreeRequirements(const Player* player, uint32 spellId)
{
    if (!player)
        return false;

    // 检查天赋树深度要求
    // 集成天赋系统来计算各个天赋树的投入点数

    uint8 playerClass = player->getClass();
    uint8 activeSpec = player->GetActiveSpec();

    // 获取玩家在各个天赋树的投入点数
    uint32 talentPoints[3] = {0, 0, 0}; // 三个天赋树的点数

    // 遍历所有天赋，计算各个天赋树的投入点数
    for (uint32 i = 0; i < sTalentStore.GetNumRows(); ++i)
    {
        TalentEntry const* talentInfo = sTalentStore.LookupEntry(i);
        if (!talentInfo)
            continue;

        TalentTabEntry const* talentTabInfo = sTalentTabStore.LookupEntry(talentInfo->TalentTab);
        if (!talentTabInfo)
            continue;

        // 检查是否是当前职业的天赋
        if (!((1 << (playerClass - 1)) & talentTabInfo->ClassMask))
            continue;

        // 检查玩家是否学会了这个天赋的任何等级
        for (uint8 rank = 0; rank < MAX_TALENT_RANK; ++rank)
        {
            uint32 talentSpellId = talentInfo->RankID[rank];
            if (talentSpellId && player->HasTalent(talentSpellId, activeSpec))
            {
                talentPoints[talentTabInfo->tabpage] += (rank + 1);
                break; // 只计算最高等级
            }
        }
    }

    // 根据技能ID检查特定的天赋树要求
    return CheckSpecificTalentTreeRequirements(playerClass, spellId, talentPoints);
}

bool BotSkillManager::CheckWarriorTalentRequirements(const Player* player, uint32 spellId, uint8 activeSpec)
{
    if (!player)
        return false;

    // 战士特定的天赋要求检查
    switch (spellId)
    {
        // 例如：某些高级技能需要特定天赋
        // case SPELL_ID: return player->HasTalent(REQUIRED_TALENT_ID, activeSpec);
        default:
            return true;
    }
}

bool BotSkillManager::CheckPaladinTalentRequirements(const Player* player, uint32 spellId, uint8 activeSpec)
{
    if (!player)
        return false;

    // 圣骑士特定的天赋要求检查
    switch (spellId)
    {
        // 例如：某些印记需要特定天赋
        // case SPELL_ID: return player->HasTalent(REQUIRED_TALENT_ID, activeSpec);
        default:
            return true;
    }
}

bool BotSkillManager::CheckMageTalentRequirements(const Player* player, uint32 spellId, uint8 activeSpec)
{
    if (!player)
        return false;

    // 法师特定的天赋要求检查
    switch (spellId)
    {
        // 例如：某些传送门技能需要特定天赋
        // case SPELL_ID: return player->HasTalent(REQUIRED_TALENT_ID, activeSpec);
        default:
            return true;
    }
}

bool BotSkillManager::CheckSpecificTalentTreeRequirements(uint8 playerClass, uint32 spellId, uint32 talentPoints[3])
{
    // 定义需要特定天赋树深度的技能
    struct TalentTreeRequirement
    {
        uint8 treeIndex;    // 天赋树索引 (0, 1, 2)
        uint32 minPoints;   // 最少需要的点数
    };

    // 根据职业和技能ID定义天赋树要求
    static std::unordered_map<uint8, std::unordered_map<uint32, TalentTreeRequirement>> classTalentTreeRequirements = {
        // 战士
        {CLASS_WARRIOR, {
            // 例如：某些高级技能需要在特定天赋树投入至少X点
            // {SPELL_ID, {TREE_INDEX, MIN_POINTS}}
        }},

        // 圣骑士
        {CLASS_PALADIN, {
            // 例如：某些高级印记需要在神圣天赋树投入点数
        }},

        // 法师
        {CLASS_MAGE, {
            // 例如：某些高级传送门需要在奥术天赋树投入点数
        }},

        // 其他职业...
    };

    auto classIt = classTalentTreeRequirements.find(playerClass);
    if (classIt != classTalentTreeRequirements.end())
    {
        auto spellIt = classIt->second.find(spellId);
        if (spellIt != classIt->second.end())
        {
            const TalentTreeRequirement& requirement = spellIt->second;
            return talentPoints[requirement.treeIndex] >= requirement.minPoints;
        }
    }

    // 如果没有特定要求，则允许学习
    return true;
}

uint32 BotSkillManager::GetPlayerTalentTreePoints(const Player* player, uint8 treeIndex)
{
    if (!player || treeIndex >= 3)
        return 0;

    uint8 playerClass = player->getClass();
    uint8 activeSpec = player->GetActiveSpec();
    uint32 points = 0;

    // 遍历所有天赋，计算指定天赋树的投入点数
    for (uint32 i = 0; i < sTalentStore.GetNumRows(); ++i)
    {
        TalentEntry const* talentInfo = sTalentStore.LookupEntry(i);
        if (!talentInfo)
            continue;

        TalentTabEntry const* talentTabInfo = sTalentTabStore.LookupEntry(talentInfo->TalentTab);
        if (!talentTabInfo)
            continue;

        // 检查是否是当前职业的天赋且是指定的天赋树
        if (!((1 << (playerClass - 1)) & talentTabInfo->ClassMask) || talentTabInfo->tabpage != treeIndex)
            continue;

        // 检查玩家是否学会了这个天赋的任何等级
        for (uint8 rank = 0; rank < MAX_TALENT_RANK; ++rank)
        {
            uint32 talentSpellId = talentInfo->RankID[rank];
            if (talentSpellId && player->HasTalent(talentSpellId, activeSpec))
            {
                points += (rank + 1);
                break; // 只计算最高等级
            }
        }
    }

    return points;
}

std::string BotSkillManager::GetPlayerTalentSpecialization(const Player* player)
{
    if (!player)
        return "未知";

    uint8 playerClass = player->getClass();

    // 获取各个天赋树的投入点数
    uint32 tree0Points = GetPlayerTalentTreePoints(player, 0);
    uint32 tree1Points = GetPlayerTalentTreePoints(player, 1);
    uint32 tree2Points = GetPlayerTalentTreePoints(player, 2);

    // 找出投入点数最多的天赋树
    uint8 primaryTree = 0;
    uint32 maxPoints = tree0Points;

    if (tree1Points > maxPoints)
    {
        primaryTree = 1;
        maxPoints = tree1Points;
    }

    if (tree2Points > maxPoints)
    {
        primaryTree = 2;
        maxPoints = tree2Points;
    }

    // 根据职业和主要天赋树返回专精名称
    return GetTalentTreeName(playerClass, primaryTree);
}

std::string BotSkillManager::GetTalentTreeName(uint8 playerClass, uint8 treeIndex)
{
    // 定义各职业的天赋树名称
    static std::unordered_map<uint8, std::vector<std::string>> classTalentTreeNames = {
        {CLASS_WARRIOR, {"武器", "狂暴", "防护"}},
        {CLASS_PALADIN, {"神圣", "防护", "惩戒"}},
        {CLASS_HUNTER, {"兽王", "射击", "生存"}},
        {CLASS_ROGUE, {"刺杀", "战斗", "敏锐"}},
        {CLASS_PRIEST, {"戒律", "神圣", "暗影"}},
        {CLASS_SHAMAN, {"元素", "增强", "恢复"}},
        {CLASS_MAGE, {"奥术", "火焰", "冰霜"}},
        {CLASS_WARLOCK, {"痛苦", "恶魔", "毁灭"}},
        {CLASS_DRUID, {"平衡", "野性", "恢复"}},
        {CLASS_DEATH_KNIGHT, {"鲜血", "冰霜", "邪恶"}},
    };

    auto it = classTalentTreeNames.find(playerClass);
    if (it != classTalentTreeNames.end() && treeIndex < it->second.size())
    {
        return it->second[treeIndex];
    }

    return "未知";
}
