#include "BotBehaviorEngine.h"
#include "BotEventSystem.h"
#include "PlayerPatch.h"
#include <map>
#include "Log.h"
#include "Player.h"
#include "Unit.h"
#include "Creature.h"
#include "GameObject.h"
#include "WorldSession.h"
#include "WorldPacket.h"
#include "ObjectMgr.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include "Cell.h"
#include "CellImpl.h"
#include <ctime>
#include <cmath>

// BotBehaviorEngine 实现

void BotBehaviorEngine::Initialize()
{
    // 初始化随机数生成器
    randomGenerator.seed(static_cast<uint32>(time(nullptr)));
    
    LOG_INFO("server", "YGbot行为引擎初始化完成");
}

void BotBehaviorEngine::UpdateBot(Player* bot, uint32 diff)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // ✅ 检查机器人是否在战斗中，如果在战斗中则不执行随机行为
    bool inCombat = bot->IsInCombat();
    bool hasTarget = bot->GetTarget() && bot->GetSelectedUnit();

    // ✅ 检查是否使用新PlayerAI系统处理组队逻辑
    if (!inCombat && !hasTarget && bot->GetGroup())
    {
        // 如果机器人在队伍中，让PlayerAI系统处理，不在这里重复处理
        return;
    }

    if (inCombat || hasTarget)
    {
        return;
    }

    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];

    // 更新行为状态
    UpdateBehaviorState(bot);

    // 检查是否需要执行行为
    uint32 currentTime = static_cast<uint32>(time(nullptr) * 1000);
    if (currentTime < state.lastActionTime + state.nextActionDelay)
        return;

    // 根据当前模式执行行为（仅在非战斗时）
    switch (state.currentMode)
    {
        case BEHAVIOR_IDLE:
            ExecuteIdleBehavior(bot);
            break;
        case BEHAVIOR_EXPLORING:
            ExecuteExploringBehavior(bot);
            break;
        case BEHAVIOR_COMBAT:
            break;
        case BEHAVIOR_SOCIAL:
            ExecuteSocialBehavior(bot);
            break;
        case BEHAVIOR_QUESTING:
            ExecuteQuestingBehavior(bot);
            break;
        case BEHAVIOR_TRADING:
            ExecuteTradingBehavior(bot);
            break;
    }

    // 更新时间戳
    state.lastActionTime = currentTime;
    state.nextActionDelay = GetRandomDelay(2000, 8000); // 2-8秒随机延迟
}

void BotBehaviorEngine::SimulatePlayerBehavior(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;
    
    // 检查机器人是否在队伍中或被设置为跟随模式
    if (bot->GetGroup() || bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
    {
 return;
    }

    // 随机选择要模拟的行为
    std::uniform_int_distribution<> behaviorDist(1, 100);
    int chance = behaviorDist(randomGenerator);
    
    if (chance <= 40) // 40% 概率移动
    {
        GenerateMovementOpcode(bot);
    }
    else if (chance <= 60) // 20% 概率聊天/表情
    {
        if (chance <= 50)
            GenerateChatOpcode(bot);
        else
            sBotOpcodeSimulator->SimulateEmote(bot, 1 + (chance % 10));
    }
    else if (chance <= 80 && bot->IsInCombat()) // 20% 概率战斗行为
    {
        // ✅ 重构：战斗行为现在由BotEventHandlers统一管理
        // 这里不再直接生成战斗操作码，避免与事件系统冲突
        LOG_DEBUG("server", "机器人 {} 在战斗中，战斗逻辑由CombatEventHandler处理", bot->GetName());
    }
    else if (chance <= 90) // 10% 概率交互
    {
        GenerateInteractionOpcode(bot);
    }
    // 10% 概率什么都不做 (保持真实感)
}

void BotBehaviorEngine::SetBehaviorMode(Player* bot, BehaviorMode mode)
{
    if (!bot)
        return;
    
    uint64 guid = bot->GetGUID().GetCounter();
    botStates[guid].currentMode = mode;
}

BotBehaviorEngine::BehaviorMode BotBehaviorEngine::GetBehaviorMode(Player* bot)
{
    if (!bot)
        return BEHAVIOR_IDLE;

    uint64 guid = bot->GetGUID().GetCounter();
    return botStates[guid].currentMode;
}

void BotBehaviorEngine::ExecuteIdleBehavior(Player* bot)
{
    // 检查机器人是否在队伍中
    if (bot->GetGroup())
    {
        // 在队伍中时，停止随机行为，保持待机状态
        return;
    }
    
    // 检查机器人是否被设置为跟随模式（通过PACIFIED标记）
    if (bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
    {
        return;
    }
    
    // 空闲行为：随机移动、表情、或切换到探索模式
    if (ShouldPerformAction(bot, 30)) // 30% 概率
    {
        std::uniform_int_distribution<> actionDist(1, 3);
        switch (actionDist(randomGenerator))
        {
            case 1:
            GenerateMovementOpcode(bot);
            break;
            case 2:
            sBotOpcodeSimulator->SimulateEmote(bot, 1 + (actionDist(randomGenerator) % 5));
            break;
            case 3:
            SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_EXPLORING);
                break;
            }
        }
    }

    void BotBehaviorEngine::ExecuteExploringBehavior(Player* bot)
{
    // 检查机器人是否在队伍中
    if (bot->GetGroup())
    {
        // 在队伍中时，切换到空闲模式，停止探索
        SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
        return;
    }
    
    // 检查机器人是否被设置为跟随模式（通过PACIFIED标记）
    if (bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
    {
        // 机器人处于跟随模式，切换到空闲模式
        SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
        return;
    }

    // 探索行为：主动移动和寻找目标
    if (bot->IsInCombat())
    if (ShouldPerformAction(bot, 70)) // 70% 概率移动
    {
        GenerateMovementOpcode(bot);
    }
    
    // 检查是否发现敌人
    if (bot->IsInCombat())
    {
        SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_COMBAT);
    }
    else if (ShouldPerformAction(bot, 10)) // 10% 概率切换到社交模式
    {
        SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_SOCIAL);
    }
}

void BotBehaviorEngine::ExecuteCombatBehavior(Player* bot)
{
    // 简化的战斗行为处理
    if (!bot->IsInCombat())
    {
        SetBehaviorMode(bot, BEHAVIOR_EXPLORING);
        return;
    }

    // 回退到传统战斗逻辑或简化处理
    if (!bot->IsInCombat())
    {
        SetBehaviorMode(bot, BEHAVIOR_EXPLORING);
        return;
    }
}

void BotBehaviorEngine::ExecuteSocialBehavior(Player* bot)
{
    // 社交行为：聊天、表情、交互
    if (ShouldPerformAction(bot, 40)) // 40% 概率
    {
        std::uniform_int_distribution<> socialDist(1, 3);
        switch (socialDist(randomGenerator))
        {
            case 1:
                GenerateChatOpcode(bot);
                break;
            case 2:
                sBotOpcodeSimulator->SimulateEmote(bot, 1 + (socialDist(randomGenerator) % 10));
                break;
            case 3:
                GenerateInteractionOpcode(bot);
                break;
        }
    }
    
    // 随机切换回其他模式
    if (ShouldPerformAction(bot, 20))
    {
        SetBehaviorMode(bot, BEHAVIOR_EXPLORING);
    }
}

void BotBehaviorEngine::ExecuteQuestingBehavior(Player* bot)
{
    // 任务行为：寻找NPC、接受任务、完成任务
    GenerateInteractionOpcode(bot);
    
    if (ShouldPerformAction(bot, 30))
    {
        SetBehaviorMode(bot, BEHAVIOR_EXPLORING);
    }
}

void BotBehaviorEngine::ExecuteTradingBehavior(Player* bot)
{
    // 交易行为：寻找商人、购买物品
    GenerateInteractionOpcode(bot);
    
    if (ShouldPerformAction(bot, 50))
    {
        SetBehaviorMode(bot, BEHAVIOR_IDLE);
    }
}

void BotBehaviorEngine::GenerateMovementOpcode(Player* bot)
{
    // 简化的移动处理
    {
        // 使用简化的移动
        float x = bot->GetPositionX();
        float y = bot->GetPositionY();
        float z = bot->GetPositionZ();

        std::uniform_real_distribution<float> angleDist(0.0f, 2.0f * M_PI);
        std::uniform_real_distribution<float> distDist(5.0f, 15.0f);

        float angle = angleDist(randomGenerator);
        float dist = distDist(randomGenerator);

        float newX = x + dist * cos(angle);
        float newY = y + dist * sin(angle);
        float newZ = z;

        bot->UpdateAllowedPositionZ(newX, newY, newZ);

        // 直接使用MotionMaster
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MovePoint(0, newX, newY, newZ);

        LOG_DEBUG("server", "机器人 {} 使用备用移动方案", bot->GetName());
    }
}

void BotBehaviorEngine::GenerateChatOpcode(Player* bot)
{
    // 生成随机聊天
    std::vector<std::string> messages = {
        "Hello everyone!",
        "How is everyone doing?",
        "Nice day today!",
        "Anyone want to group up?",
        "Looking for adventure!",
        "This place is interesting.",
        "Good luck to all!",
        "Have a great day!"
    };
    
    std::uniform_int_distribution<> msgDist(0, messages.size() - 1);
    std::string message = messages[msgDist(randomGenerator)];
    
    sBotOpcodeSimulator->SimulateChat(bot, message, 0); // SAY
}

void BotBehaviorEngine::GenerateInteractionOpcode(Player* bot)
{
    // 寻找附近的NPC进行交互
    std::list<Creature*> creatures;
    bot->GetCreatureListWithEntryInGrid(creatures, 0, 10.0f);
    
    for (auto* creature : creatures)
    {
        if (creature->IsAlive() && !creature->IsHostileTo(bot))
        {
            sBotOpcodeSimulator->SimulateNPCInteraction(bot, creature);
            break;
        }
    }
}

uint32 BotBehaviorEngine::GetRandomDelay(uint32 min, uint32 max)
{
    std::uniform_int_distribution<uint32> delayDist(min, max);
    return delayDist(randomGenerator);
}

bool BotBehaviorEngine::ShouldPerformAction(Player* bot, uint32 probability)
{
    std::uniform_int_distribution<> chanceDist(1, 100);
    return chanceDist(randomGenerator) <= static_cast<int>(probability);
}

void BotBehaviorEngine::UpdateBehaviorState(Player* bot)
{
    uint64 guid = bot->GetGUID().GetCounter();
    auto& state = botStates[guid];
    
    // 根据游戏状态自动调整行为模式
    if (bot->IsInCombat() && state.currentMode != BEHAVIOR_COMBAT)
    {
        SetBehaviorMode(bot, BEHAVIOR_COMBAT);
    }
    else if (!bot->IsInCombat() && state.currentMode == BEHAVIOR_COMBAT)
    {
        SetBehaviorMode(bot, BEHAVIOR_EXPLORING);
    }
}

// BotOpcodeSimulator 实现

void BotOpcodeSimulator::SimulateMovement(Player* bot, float x, float y, float z, float orientation)
{
    if (!bot || !bot->GetSession())
        return;

    // 先发送移动开始操作码
    CreateAndSendPacket(bot, CMSG_MOVE_START_FORWARD, [&](WorldPacket& packet) {
        packet << bot->GetGUID().GetCounter();
        packet << static_cast<uint32>(0); // flags
        packet << static_cast<uint32>(time(nullptr)); // time
        packet << x << y << z << orientation;
    });

    // 触发移动事件
    sBotEventMgr->TriggerMoveEvent(BotEventType::BOT_EVENT_MOVE_START, bot, x, y, z, orientation,
                                   bot->GetMapId(), bot->GetZoneId(), bot->GetAreaId());

    LOG_TRACE("server", "BotOpcodeSimulator: 机器人 {} 模拟移动到 ({:.2f}, {:.2f}, {:.2f})",
              bot->GetName(), x, y, z);
}

void BotOpcodeSimulator::SimulateMovementStart(Player* bot, uint32 moveType)
{
    if (!bot || !bot->GetSession())
        return;

    uint16 opcode = CMSG_MOVE_START_FORWARD;
    switch (moveType)
    {
        case 1: opcode = CMSG_MOVE_START_BACKWARD; break;
        case 2: opcode = CMSG_MOVE_START_FORWARD; break;
        default: break;
    }

    CreateAndSendPacket(bot, opcode, [&](WorldPacket& packet) {
        packet << bot->GetGUID().GetCounter();
        packet << static_cast<uint32>(0);
        packet << static_cast<uint32>(time(nullptr));
        packet << bot->GetPositionX() << bot->GetPositionY() << bot->GetPositionZ() << bot->GetOrientation();
    });

    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_MOVE_START, bot);
}

void BotOpcodeSimulator::SimulateMovementStop(Player* bot)
{
    if (!bot || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_MOVE_STOP, [&](WorldPacket& packet) {
        packet << bot->GetGUID().GetCounter();
        packet << static_cast<uint32>(0);
        packet << static_cast<uint32>(time(nullptr));
        packet << bot->GetPositionX() << bot->GetPositionY() << bot->GetPositionZ() << bot->GetOrientation();
    });

    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_MOVE_STOP, bot);
}

void BotOpcodeSimulator::SimulateChat(Player* bot, const std::string& message, uint32 chatType)
{
    if (!bot || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_MESSAGECHAT, [&](WorldPacket& packet) {
        packet << static_cast<uint32>(chatType); // CHAT_MSG_SAY = 0
        packet << static_cast<uint32>(0); // language
        packet << message;
    });

    sBotEventMgr->TriggerChatEvent(BotEventType::BOT_EVENT_CHAT_SAY, bot, message, chatType, 0);
}

void BotOpcodeSimulator::SimulateEmote(Player* bot, uint32 emoteId)
{
    if (!bot || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_TEXT_EMOTE, [&](WorldPacket& packet) {
        packet << static_cast<uint32>(emoteId);
        packet << static_cast<uint64>(0); // target guid
    });

    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_EMOTE, bot);
}

void BotOpcodeSimulator::SimulateAttack(Player* bot, Unit* target)
{
    if (!bot || !target || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_ATTACKSWING, [&](WorldPacket& packet) {
        packet << target->GetGUID().GetCounter();
    });

    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_ATTACK, bot, target);
}

void BotOpcodeSimulator::SimulateSpellCast(Player* bot, uint32 spellId, Unit* target)
{
    if (!bot || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_CAST_SPELL, [&](WorldPacket& packet) {
        packet << static_cast<uint32>(spellId);
        if (target)
            packet << target->GetGUID().GetCounter();
        else
            packet << static_cast<uint64>(0);
    });

    sBotEventMgr->TriggerCombatEvent(BotEventType::BOT_EVENT_SPELL_CAST, bot, target, 0, spellId);
}

void BotOpcodeSimulator::SimulateCombatMovement(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    // 计算战斗移动位置 (绕着目标移动)
    float angle = bot->GetAngle(target) + M_PI / 4; // 45度偏移
    float dist = 3.0f; // 保持3码距离

    float x = target->GetPositionX() + dist * cos(angle);
    float y = target->GetPositionY() + dist * sin(angle);
    float z = target->GetPositionZ();

    bot->UpdateAllowedPositionZ(x, y, z);
    SimulateMovement(bot, x, y, z, bot->GetAngle(target));
}

void BotOpcodeSimulator::SimulateNPCInteraction(Player* bot, Creature* npc)
{
    if (!bot || !npc || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_GOSSIP_HELLO, [&](WorldPacket& packet) {
        packet << npc->GetGUID().GetCounter();
    });

    sBotEventMgr->TriggerEvent(BotEventType::BOT_EVENT_GOSSIP_HELLO, bot);
}

void BotOpcodeSimulator::SimulateItemUse(Player* bot, uint32 itemId)
{
    if (!bot || !bot->GetSession())
        return;

    CreateAndSendPacket(bot, CMSG_USE_ITEM, [&](WorldPacket& packet) {
        packet << static_cast<uint8>(0); // bag
        packet << static_cast<uint8>(0); // slot
        packet << static_cast<uint32>(itemId);
    });

    sBotEventMgr->TriggerItemEvent(BotEventType::BOT_EVENT_ITEM_USE, bot, itemId);
}

void BotOpcodeSimulator::CreateAndSendPacket(Player* bot, uint16 opcode, std::function<void(WorldPacket&)> packetBuilder)
{
    if (!bot || !bot->GetSession())
        return;

    try
    {
        WorldPacket packet(opcode);
        packetBuilder(packet);

        // 直接处理操作码，模拟客户端发送
        sBotOpcodeMapper->HandleOpcode(bot, opcode, packet);
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "BotOpcodeSimulator: 创建操作码包时发生异常: {}", e.what());
    }
}

// ✅ PlayerAI系统集成入口 - 替代原有的组队移动处理
void BotBehaviorEngine::HandleGroupMovement(Player* bot)
{
    if (!bot || !bot->IsInWorld() || !bot->IsAlive())
        return;

    // 检查是否在队伍中
    Group* group = bot->GetGroup();
    if (!group)
        return;

    // 简化的组队逻辑处理
    LOG_DEBUG("server", "BotBehaviorEngine: 处理机器人 {} 的组队逻辑", bot->GetName());
}

// 空的脚本注册函数 - 这个文件包含行为引擎实现，不是脚本
void AddSC_BotBehaviorEngine()
{
    // 这个文件只包含行为引擎实现，不需要注册脚本
}
